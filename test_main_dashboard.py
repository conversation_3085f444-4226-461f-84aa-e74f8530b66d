#!/usr/bin/env python3
"""
Test script to check main dashboard display with elite metrics
"""
import sys
import asyncio
sys.path.append('.')

from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
from utils.config_manager_v2_5 import ConfigManagerV2_5
from dashboard_application.modes.main_dashboard_display_v2_5 import create_layout

async def test_main_dashboard():
    try:
        print("🔍 Initializing components...")
        config = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config)
        
        print("🔍 Getting analysis bundle...")
        bundle = await orchestrator.run_full_analysis_cycle('SPX', dte_min=0, dte_max=5, price_range_percent=5)
        
        print("🔍 Testing main dashboard layout creation...")
        layout = create_layout(bundle, config)
        
        # Check the underlying data
        und_data = bundle.processed_data_bundle.underlying_data_enriched
        print(f"✅ Elite Impact Score: {getattr(und_data, 'elite_impact_score_und', 'NOT_SET')}")
        print(f"✅ Flow Momentum Index: {getattr(und_data, 'flow_momentum_index_und', 'NOT_SET')}")
        print(f"✅ Institutional Flow Score: {getattr(und_data, 'institutional_flow_score_und', 'NOT_SET')}")
        print(f"✅ Market Regime Elite: {getattr(und_data, 'market_regime_elite', 'NOT_SET')}")
        print(f"✅ Flow Type Elite: {getattr(und_data, 'flow_type_elite', 'NOT_SET')}")
        print(f"✅ Volatility Regime Elite: {getattr(und_data, 'volatility_regime_elite', 'NOT_SET')}")
        
        print("✅ Main dashboard layout created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_main_dashboard())
    if success:
        print("🎉 Main dashboard test completed successfully")
    else:
        print("💥 Main dashboard test failed")
