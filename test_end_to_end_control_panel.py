#!/usr/bin/env python3
"""
End-to-End Control Panel Integration Test

This script simulates the complete dashboard workflow to verify that control panel
settings flow properly from the UI through the callback system to the dashboard modes.

WORKFLOW TESTED:
1. User sets control panel parameters (DTE range, price range %, refresh interval)
2. Data fetching callback captures these parameters
3. Control panel state is created and stored
4. Mode rendering callback receives both data bundle and control panel state
5. Dashboard modes receive and can use the control panel parameters
6. Settings remain consistent across mode switches

Author: AI Assistant
Date: 2025-01-27
"""

import sys
import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simulate_control_panel_input():
    """Simulate user input to control panel"""
    logger.info("🎛️ SIMULATING CONTROL PANEL INPUT")
    
    # Simulate user setting control panel values
    user_inputs = {
        'symbol': 'SPX',
        'dte_min': 1,
        'dte_max': 7,
        'price_range_percent': 15,
        'refresh_interval': 60
    }
    
    logger.info(f"User sets: Symbol={user_inputs['symbol']}, "
               f"DTE {user_inputs['dte_min']}-{user_inputs['dte_max']}, "
               f"Price Range ±{user_inputs['price_range_percent']}%, "
               f"Refresh {user_inputs['refresh_interval']}s")
    
    return user_inputs

def simulate_data_fetching_callback(user_inputs):
    """Simulate the data fetching callback that creates control panel state"""
    logger.info("🔄 SIMULATING DATA FETCHING CALLBACK")
    
    try:
        from data_models.core_system_config import ControlPanelStateV2_5
        
        # Create control panel state (as done in callback_manager_v2_5.py)
        control_panel_state = ControlPanelStateV2_5(
            symbol=user_inputs['symbol'],
            dte_min=user_inputs['dte_min'],
            dte_max=user_inputs['dte_max'],
            price_range_percent=user_inputs['price_range_percent'],
            refresh_interval_seconds=user_inputs['refresh_interval']
        )
        
        # Serialize for storage (as done in the callback)
        control_panel_state_json = control_panel_state.model_dump_json()
        
        logger.info(f"✅ Control panel state created and serialized")
        logger.info(f"State: {control_panel_state.symbol}, DTE {control_panel_state.dte_min}-{control_panel_state.dte_max}")
        
        return control_panel_state_json, control_panel_state
        
    except Exception as e:
        logger.error(f"❌ Data fetching callback simulation failed: {e}")
        return None, None

def simulate_mode_rendering_callback(control_panel_state_json, mode_name):
    """Simulate the mode rendering callback that passes state to modes"""
    logger.info(f"🎨 SIMULATING MODE RENDERING CALLBACK FOR {mode_name.upper()}")
    
    try:
        from data_models.core_system_config import ControlPanelStateV2_5
        
        # Parse control panel state (as done in callback_manager_v2_5.py)
        if control_panel_state_json:
            control_panel_state = ControlPanelStateV2_5.model_validate_json(control_panel_state_json)
            logger.info(f"✅ Control panel state parsed for {mode_name}")
            logger.info(f"Parsed state: {control_panel_state.symbol}, DTE {control_panel_state.dte_min}-{control_panel_state.dte_max}")
            return control_panel_state
        else:
            logger.warning(f"⚠️ No control panel state available for {mode_name}")
            return None
            
    except Exception as e:
        logger.error(f"❌ Mode rendering callback simulation failed for {mode_name}: {e}")
        return None

def simulate_mode_usage(mode_name, control_panel_state):
    """Simulate how a dashboard mode would use the control panel state"""
    logger.info(f"📊 SIMULATING {mode_name.upper()} MODE USAGE")
    
    try:
        if control_panel_state:
            # Simulate mode using control panel parameters
            logger.info(f"✅ {mode_name} mode received control panel state:")
            logger.info(f"  - Symbol: {control_panel_state.symbol}")
            logger.info(f"  - DTE Range: {control_panel_state.dte_min} to {control_panel_state.dte_max}")
            logger.info(f"  - Price Range: ±{control_panel_state.price_range_percent}%")
            logger.info(f"  - Refresh Interval: {control_panel_state.refresh_interval_seconds}s")
            
            # Simulate mode-specific logic using these parameters
            if mode_name == "volatility":
                logger.info(f"  🌊 Volatility mode: Filtering volatility data for DTE {control_panel_state.dte_min}-{control_panel_state.dte_max}")
                logger.info(f"  🌊 Volatility mode: Using ±{control_panel_state.price_range_percent}% price range for strike filtering")
            elif mode_name == "flow":
                logger.info(f"  💧 Flow mode: Analyzing flow patterns for DTE {control_panel_state.dte_min}-{control_panel_state.dte_max}")
                logger.info(f"  💧 Flow mode: Heatmaps filtered to ±{control_panel_state.price_range_percent}% price range")
            elif mode_name == "structure":
                logger.info(f"  🏗️ Structure mode: Dealer positioning analysis for DTE {control_panel_state.dte_min}-{control_panel_state.dte_max}")
                logger.info(f"  🏗️ Structure mode: Strike charts limited to ±{control_panel_state.price_range_percent}% range")
            
            return True
        else:
            logger.error(f"❌ {mode_name} mode: No control panel state received!")
            return False
            
    except Exception as e:
        logger.error(f"❌ {mode_name} mode simulation failed: {e}")
        return False

def test_mode_switching_consistency(control_panel_state_json):
    """Test that control panel state remains consistent across mode switches"""
    logger.info("🔄 TESTING MODE SWITCHING CONSISTENCY")
    
    modes_to_test = ['main', 'volatility', 'flow', 'structure', 'time_decay']
    results = []
    
    for mode in modes_to_test:
        logger.info(f"\n--- Switching to {mode.upper()} mode ---")
        
        # Simulate mode rendering callback
        state = simulate_mode_rendering_callback(control_panel_state_json, mode)
        
        # Simulate mode usage
        success = simulate_mode_usage(mode, state)
        results.append((mode, success))
        
        if success:
            logger.info(f"✅ {mode} mode: Control panel settings applied correctly")
        else:
            logger.error(f"❌ {mode} mode: Failed to apply control panel settings")
    
    # Check consistency
    successful_modes = [mode for mode, success in results if success]
    logger.info(f"\n📊 Mode switching results: {len(successful_modes)}/{len(modes_to_test)} modes successful")
    
    return len(successful_modes) == len(modes_to_test)

def run_end_to_end_test():
    """Run the complete end-to-end test"""
    logger.info("🚀 STARTING END-TO-END CONTROL PANEL INTEGRATION TEST")
    logger.info("=" * 70)
    
    try:
        # Step 1: Simulate user input
        user_inputs = simulate_control_panel_input()
        
        # Step 2: Simulate data fetching callback
        logger.info("\n" + "=" * 70)
        control_panel_state_json, control_panel_state = simulate_data_fetching_callback(user_inputs)
        
        if not control_panel_state_json:
            logger.error("❌ Data fetching callback failed - cannot continue")
            return False
        
        # Step 3: Test mode switching consistency
        logger.info("\n" + "=" * 70)
        consistency_test_passed = test_mode_switching_consistency(control_panel_state_json)
        
        # Step 4: Verify parameter persistence
        logger.info("\n" + "=" * 70)
        logger.info("🔍 VERIFYING PARAMETER PERSISTENCE")
        
        # Re-parse state to simulate persistence across callbacks
        reparsed_state = simulate_mode_rendering_callback(control_panel_state_json, "test")
        
        if reparsed_state:
            # Verify all parameters match original input
            params_match = (
                reparsed_state.symbol == user_inputs['symbol'] and
                reparsed_state.dte_min == user_inputs['dte_min'] and
                reparsed_state.dte_max == user_inputs['dte_max'] and
                reparsed_state.price_range_percent == user_inputs['price_range_percent'] and
                reparsed_state.refresh_interval_seconds == user_inputs['refresh_interval']
            )
            
            if params_match:
                logger.info("✅ Parameter persistence: All parameters preserved correctly")
            else:
                logger.error("❌ Parameter persistence: Parameters were modified during serialization/deserialization")
                return False
        else:
            logger.error("❌ Parameter persistence: Failed to reparse state")
            return False
        
        # Final result
        logger.info("\n" + "=" * 70)
        logger.info("📊 END-TO-END TEST RESULTS")
        logger.info("=" * 70)
        
        if consistency_test_passed:
            logger.info("🎉 SUCCESS: Control panel settings flow correctly through the entire system!")
            logger.info("✅ User input → Data fetching → State creation → Mode rendering → Mode usage")
            logger.info("✅ Settings remain consistent across all dashboard modes")
            logger.info("✅ Parameters are preserved during serialization/deserialization")
            return True
        else:
            logger.error("❌ FAILURE: Some modes did not receive or use control panel settings correctly")
            return False
            
    except Exception as e:
        logger.error(f"💥 End-to-end test crashed: {e}")
        return False

if __name__ == "__main__":
    success = run_end_to_end_test()
    sys.exit(0 if success else 1)
