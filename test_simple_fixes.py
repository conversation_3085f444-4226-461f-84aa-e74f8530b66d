# test_simple_fixes.py

"""
Simple test to verify the core fixes work without complex dependencies.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

def test_pydantic_imports():
    """Test that all Pydantic models can be imported"""
    print("🧪 Testing Pydantic Model Imports...")
    
    try:
        # Test expert_ai_config
        from data_models.expert_ai_config import AnalyticsEngineConfigV2_5
        print("  ✅ expert_ai_config imports successfully")
        
        # Test current data models structure
        from data_models import ProcessedDataBundleV2_5, TickerContextDictV2_5, SignalPayloadV2_5
        print("  ✅ current data models import successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Pydantic import test failed: {e}")
        return False

def test_convexlib_import():
    """Test that ConvexLib can be imported"""
    print("🧪 Testing ConvexLib Import...")
    
    try:
        import convexlib
        print("  ✅ convexlib imports successfully")
        return True
        
    except ImportError as e:
        print(f"  ❌ ConvexLib import failed: {e}")
        return False

def test_consolidated_modules_basic():
    """Test basic import of consolidated modules"""
    print("🧪 Testing Consolidated Modules Basic Import...")
    
    try:
        # Add the eots_metrics path
        sys.path.insert(0, 'core_analytics_engine/eots_metrics')
        
        # Test each consolidated module
        from elite_intelligence import EliteImpactCalculator, EliteConfig
        print("  ✅ elite_intelligence imports successfully")
        
        from supplementary_metrics import SupplementaryMetrics
        print("  ✅ supplementary_metrics imports successfully")
        
        # Test creating instances
        config = EliteConfig()
        calculator = EliteImpactCalculator(config)
        print("  ✅ EliteImpactCalculator instance created successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Consolidated modules test failed: {e}")
        return False

def test_pydantic_v2_functionality():
    """Test that Pydantic v2 functionality works"""
    print("🧪 Testing Pydantic v2 Functionality...")
    
    try:
        from data_models.expert_ai_config import AnalyticsEngineConfigV2_5
        
        # Create instance
        config = AnalyticsEngineConfigV2_5()
        print("  ✅ Config instance created")
        
        # Test model_dump (v2 syntax)
        config_dict = config.model_dump()
        print("  ✅ model_dump() works (Pydantic v2)")
        
        # Test model_validate (v2 syntax)
        new_config = AnalyticsEngineConfigV2_5.model_validate(config_dict)
        print("  ✅ model_validate() works (Pydantic v2)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Pydantic v2 functionality test failed: {e}")
        return False

def main():
    """Run all simple fix tests"""
    print("🚀 Testing Core Fixes (Simple Version)\n")
    
    tests = [
        ("Pydantic Model Imports", test_pydantic_imports),
        ("ConvexLib Import", test_convexlib_import),
        ("Consolidated Modules Basic", test_consolidated_modules_basic),
        ("Pydantic v2 Functionality", test_pydantic_v2_functionality)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"📋 {test_name}")
        try:
            if test_func():
                passed += 1
                print("  🎉 PASSED\n")
            else:
                failed += 1
                print("  ❌ FAILED\n")
        except Exception as e:
            print(f"  💥 CRASHED: {e}\n")
            failed += 1
    
    print("=" * 60)
    print("📊 SIMPLE FIX TEST RESULTS")
    print("=" * 60)
    print(f"✅ Passed: {passed}/4")
    print(f"❌ Failed: {failed}/4")
    
    if failed == 0:
        print("\n🎉 ALL CORE FIXES WORKING!")
        print("✅ Pydantic v1/v2 conflict resolved")
        print("✅ ConvexLib dependency available")
        print("✅ Consolidated modules functional")
        print("✅ System ready for production")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) still have issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
