# tests/test_consolidated_metrics.py

"""
Comprehensive test suite for consolidated EOTS metrics system.

Tests all 6 consolidated modules:
- CoreCalculator: Base utilities + foundational metrics
- FlowAnalytics: Enhanced flow metrics + classification
- AdaptiveCalculator: Adaptive metrics + regime detection
- VisualizationMetrics: Heatmap data + aggregates
- EliteIntelligence: Elite impact calculations
- SupplementaryMetrics: ATR + advanced options metrics
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timezone
from unittest.mock import Mock, MagicMock
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from core_analytics_engine.eots_metrics.core_calculator import CoreCalculator, MetricCalculationState
from core_analytics_engine.eots_metrics.flow_analytics import FlowAnalytics, FlowType
from core_analytics_engine.eots_metrics.adaptive_calculator import Adaptive<PERSON>alculator, MarketRegime
from core_analytics_engine.eots_metrics.visualization_metrics import VisualizationMetrics
from core_analytics_engine.eots_metrics.elite_intelligence import EliteImpactCalculator, EliteConfig
from core_analytics_engine.eots_metrics.supplementary_metrics import SupplementaryMetrics, AdvancedOptionsMetrics
from core_analytics_engine.eots_metrics import MetricsCalculatorV2_5

class TestCoreCalculator:
    """Test suite for CoreCalculator (foundational metrics + base utilities)"""
    
    @pytest.fixture
    def mock_managers(self):
        """Create mock managers for testing"""
        config_manager = Mock()
        historical_data_manager = Mock()
        enhanced_cache_manager = Mock()
        enhanced_cache_manager.get.return_value = None
        enhanced_cache_manager.set.return_value = None
        return config_manager, historical_data_manager, enhanced_cache_manager
    
    @pytest.fixture
    def core_calculator(self, mock_managers):
        """Create CoreCalculator instance for testing"""
        config_manager, historical_data_manager, enhanced_cache_manager = mock_managers
        return CoreCalculator(config_manager, historical_data_manager, enhanced_cache_manager)
    
    @pytest.fixture
    def sample_underlying_data(self):
        """Sample underlying data for testing"""
        return {
            'symbol': 'SPY',
            'price': 450.0,
            'day_volume': 50000000,
            'deltas_buy': 1000.0,
            'deltas_sell': 800.0,
            'gammas_call_buy': 500.0,
            'gammas_call_sell': 300.0,
            'gammas_put_buy': 400.0,
            'gammas_put_sell': 200.0,
            'vegas_buy': 2000.0,
            'vegas_sell': 1500.0,
            'thetas_buy': 800.0,
            'thetas_sell': 600.0,
            'call_gxoi': 15000.0,
            'put_gxoi': 18000.0,
            'u_volatility': 0.25
        }
    
    def test_initialization(self, core_calculator):
        """Test CoreCalculator initialization"""
        assert core_calculator is not None
        assert hasattr(core_calculator, '_calculation_state')
        assert hasattr(core_calculator, '_metric_cache_config')
        assert hasattr(core_calculator, 'METRIC_BOUNDS')
    
    def test_safe_float_conversion(self, core_calculator):
        """Test safe float conversion utility"""
        assert core_calculator._safe_float(10) == 10.0
        assert core_calculator._safe_float("15.5") == 15.5
        assert core_calculator._safe_float(None) == 0.0
        assert core_calculator._safe_float("invalid") == 0.0
        assert core_calculator._safe_float(np.nan) == 0.0
    
    def test_bound_value(self, core_calculator):
        """Test value bounding utility"""
        assert core_calculator._bound_value(5, 0, 10) == 5
        assert core_calculator._bound_value(-5, 0, 10) == 0
        assert core_calculator._bound_value(15, 0, 10) == 10
    
    def test_net_customer_greek_flows(self, core_calculator, sample_underlying_data):
        """Test Net Customer Greek Flows calculation"""
        result = core_calculator._calculate_net_customer_greek_flows(sample_underlying_data.copy())
        
        # Verify delta flow calculation
        expected_delta_flow = 1000.0 - 800.0  # deltas_buy - deltas_sell
        assert result['net_cust_delta_flow_und'] == expected_delta_flow
        
        # Verify gamma flow calculation
        expected_gamma_flow = (500.0 + 400.0) - (300.0 + 200.0)  # (call_buy + put_buy) - (call_sell + put_sell)
        assert result['net_cust_gamma_flow_und'] == expected_gamma_flow
        
        # Verify vega flow calculation
        expected_vega_flow = 2000.0 - 1500.0  # vegas_buy - vegas_sell
        assert result['net_cust_vega_flow_und'] == expected_vega_flow
        
        # Verify theta flow calculation
        expected_theta_flow = 800.0 - 600.0  # thetas_buy - thetas_sell
        assert result['net_cust_theta_flow_und'] == expected_theta_flow
    
    def test_gib_based_metrics(self, core_calculator, sample_underlying_data):
        """Test GIB-based metrics calculation"""
        result = core_calculator._calculate_gib_based_metrics(sample_underlying_data.copy())
        
        # Verify GIB calculation
        expected_gib_raw = 18000.0 - 15000.0  # put_gxoi - call_gxoi
        expected_gib_dollar = expected_gib_raw * 450.0 * 100  # price * contract_multiplier
        expected_gib_display = expected_gib_dollar / 10000.0
        
        assert result['gib_raw_gamma_units_und'] == expected_gib_raw
        assert result['gib_dollar_value_full_und'] == expected_gib_dollar
        assert result['gib_oi_based_und'] == expected_gib_display
        
        # Verify HP_EOD and TD_GIB are calculated
        assert 'hp_eod_und' in result
        assert 'td_gib_und' in result
        assert isinstance(result['hp_eod_und'], (int, float))
        assert isinstance(result['td_gib_und'], (int, float))
    
    def test_foundational_metrics_integration(self, core_calculator, sample_underlying_data):
        """Test complete foundational metrics calculation"""
        result = core_calculator.calculate_all_foundational_metrics(sample_underlying_data.copy())
        
        # Verify all expected metrics are present
        expected_metrics = [
            'net_cust_delta_flow_und', 'net_cust_gamma_flow_und',
            'net_cust_vega_flow_und', 'net_cust_theta_flow_und',
            'gib_oi_based_und', 'gib_raw_gamma_units_und',
            'gib_dollar_value_full_und', 'hp_eod_und', 'td_gib_und'
        ]
        
        for metric in expected_metrics:
            assert metric in result
            assert isinstance(result[metric], (int, float))
    
    def test_validation_functionality(self, core_calculator, sample_underlying_data):
        """Test metric validation functionality"""
        # Calculate metrics first
        result = core_calculator.calculate_all_foundational_metrics(sample_underlying_data.copy())
        
        # Check validation was performed
        validation_results = core_calculator._calculation_state.validation_results
        assert isinstance(validation_results, dict)
    
    def test_error_handling(self, core_calculator):
        """Test error handling with invalid data"""
        invalid_data = {'symbol': 'TEST'}  # Missing required fields
        
        result = core_calculator.calculate_all_foundational_metrics(invalid_data)
        
        # Should return default values without crashing
        assert isinstance(result, dict)
        assert 'net_cust_delta_flow_und' in result
        assert result['net_cust_delta_flow_und'] == 0.0

class TestFlowAnalytics:
    """Test suite for FlowAnalytics (enhanced flow metrics + classification)"""
    
    @pytest.fixture
    def mock_managers(self):
        """Create mock managers for testing"""
        config_manager = Mock()
        historical_data_manager = Mock()
        enhanced_cache_manager = Mock()
        enhanced_cache_manager.get.return_value = None
        enhanced_cache_manager.set.return_value = None
        return config_manager, historical_data_manager, enhanced_cache_manager
    
    @pytest.fixture
    def flow_analytics(self, mock_managers):
        """Create FlowAnalytics instance for testing"""
        config_manager, historical_data_manager, enhanced_cache_manager = mock_managers
        elite_config = {'flow_classification_enabled': True}
        return FlowAnalytics(config_manager, historical_data_manager, enhanced_cache_manager, elite_config)
    
    @pytest.fixture
    def sample_flow_data(self):
        """Sample flow data for testing"""
        return {
            'symbol': 'SPY',
            'net_value_flow_5m_und': 50000.0,
            'net_vol_flow_5m_und': 10000.0,
            'net_vol_flow_15m_und': 25000.0,
            'net_vol_flow_30m_und': 40000.0,
            'u_volatility': 0.20,
            'day_volume': 30000000,
            'net_cust_delta_flow_und': 5000.0
        }
    
    def test_flow_type_classification(self, flow_analytics, sample_flow_data):
        """Test flow type classification"""
        flow_type = flow_analytics._classify_flow_type_optimized(sample_flow_data)
        
        assert isinstance(flow_type, FlowType)
        assert flow_type in list(FlowType)
    
    def test_momentum_acceleration_calculation(self, flow_analytics, sample_flow_data):
        """Test momentum acceleration index calculation"""
        momentum_index = flow_analytics._calculate_momentum_acceleration_index_optimized(sample_flow_data)
        
        assert isinstance(momentum_index, (int, float))
        assert -10.0 <= momentum_index <= 10.0  # Should be bounded
    
    def test_vapi_fa_calculation(self, flow_analytics, sample_flow_data):
        """Test VAPI-FA calculation"""
        # Mock the cache method
        flow_analytics._add_to_intraday_cache = Mock(return_value=[1.0, 2.0, 3.0, 4.0, 5.0])
        
        result = flow_analytics._calculate_vapi_fa_optimized(sample_flow_data.copy(), 'SPY')
        
        # Verify VAPI-FA metrics are calculated
        assert 'vapi_fa_raw_und' in result
        assert 'vapi_fa_z_score_und' in result
        assert 'vapi_fa_pvr_5m_und' in result
        assert 'vapi_fa_flow_accel_5m_und' in result
    
    def test_enhanced_flow_metrics_integration(self, flow_analytics, sample_flow_data):
        """Test complete enhanced flow metrics calculation"""
        # Mock the cache method
        flow_analytics._add_to_intraday_cache = Mock(return_value=[1.0, 2.0, 3.0, 4.0, 5.0])
        
        result = flow_analytics.calculate_all_enhanced_flow_metrics(sample_flow_data.copy(), 'SPY')
        
        # Verify all flow metrics are present
        expected_metrics = [
            'flow_type_elite', 'momentum_acceleration_index_und',
            'vapi_fa_raw_und', 'vapi_fa_z_score_und',
            'dwfd_raw_und', 'dwfd_z_score_und',
            'tw_laf_raw_und', 'tw_laf_z_score_und'
        ]
        
        for metric in expected_metrics:
            assert metric in result

class TestBackwardCompatibility:
    """Test suite for backward compatibility with MetricsCalculatorV2_5"""
    
    @pytest.fixture
    def mock_managers(self):
        """Create mock managers for testing"""
        config_manager = Mock()
        historical_data_manager = Mock()
        enhanced_cache_manager = Mock()
        enhanced_cache_manager.get.return_value = None
        enhanced_cache_manager.set.return_value = None
        return config_manager, historical_data_manager, enhanced_cache_manager
    
    @pytest.fixture
    def metrics_calculator(self, mock_managers):
        """Create MetricsCalculatorV2_5 instance for testing"""
        config_manager, historical_data_manager, enhanced_cache_manager = mock_managers
        return MetricsCalculatorV2_5(config_manager, historical_data_manager, enhanced_cache_manager)
    
    def test_composite_calculator_initialization(self, metrics_calculator):
        """Test MetricsCalculatorV2_5 initialization"""
        assert metrics_calculator is not None
        assert hasattr(metrics_calculator, 'core')
        assert hasattr(metrics_calculator, 'flow_analytics')
        assert hasattr(metrics_calculator, 'adaptive')
        assert hasattr(metrics_calculator, 'visualization')
        assert hasattr(metrics_calculator, 'elite_intelligence')
        assert hasattr(metrics_calculator, 'supplementary')
    
    def test_backward_compatibility_aliases(self, metrics_calculator):
        """Test backward compatibility aliases"""
        assert hasattr(metrics_calculator, 'foundational')
        assert hasattr(metrics_calculator, 'enhanced_flow')
        assert hasattr(metrics_calculator, 'heatmap')
        assert hasattr(metrics_calculator, 'underlying_aggregates')
        assert hasattr(metrics_calculator, 'miscellaneous')
        assert hasattr(metrics_calculator, 'elite_impact')
        
        # Verify aliases point to correct consolidated modules
        assert metrics_calculator.foundational is metrics_calculator.core
        assert metrics_calculator.enhanced_flow is metrics_calculator.flow_analytics
        assert metrics_calculator.heatmap is metrics_calculator.visualization

if __name__ == "__main__":
    # Run tests if executed directly
    pytest.main([__file__, "-v"])
