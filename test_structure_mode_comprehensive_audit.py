#!/usr/bin/env python3
"""
Comprehensive Structure Mode Dashboard Audit and Analysis
Tests data accuracy, metric calculations, and control panel integration.
"""

import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger("StructureModeAudit")

def test_structure_mode_imports():
    """Test 1: Verify Structure Mode imports and dependencies"""
    logger.info("🧪 TEST 1: Structure Mode Imports and Dependencies")
    
    try:
        # Test core imports
        from dashboard_application.modes.structure_mode_display_v2_5 import create_layout
        from data_models import FinalAnalysisBundleV2_5, KeyLevelsDataV2_5, KeyLevelV2_5
        from data_models import ProcessedDataBundleV2_5, ProcessedStrikeLevelMetricsV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        logger.info("✅ PASSED: All Structure Mode imports successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Import error - {e}")
        return False

def test_structure_mode_chart_functions():
    """Test 2: Verify Structure Mode chart generation functions exist"""
    logger.info("🧪 TEST 2: Structure Mode Chart Generation Functions")
    
    try:
        from dashboard_application.modes import structure_mode_display_v2_5 as sm
        
        # Check for required chart functions
        required_functions = [
            '_generate_amspi_heatmap',
            '_generate_esdag_charts', 
            '_generate_adag_strike_chart',
            '_generate_asai_assi_charts',
            '_generate_key_level_table'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if not hasattr(sm, func_name):
                missing_functions.append(func_name)
        
        if missing_functions:
            logger.error(f"❌ FAILED: Missing chart functions: {missing_functions}")
            return False
        
        logger.info("✅ PASSED: All required chart functions exist")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Chart function verification error - {e}")
        return False

def test_control_panel_integration():
    """Test 3: Verify control panel state integration"""
    logger.info("🧪 TEST 3: Control Panel State Integration")
    
    try:
        from data_models.core_system_config import ControlPanelStateV2_5
        from dashboard_application.modes.structure_mode_display_v2_5 import create_layout
        
        # Create test control panel state
        control_panel_state = ControlPanelStateV2_5(
            symbol="SPX",
            dte_min=0,
            dte_max=5,
            price_range_percent=5,
            refresh_interval_seconds=30
        )
        
        # Verify control panel state structure
        assert hasattr(control_panel_state, 'symbol')
        assert hasattr(control_panel_state, 'dte_min')
        assert hasattr(control_panel_state, 'dte_max')
        assert hasattr(control_panel_state, 'price_range_percent')
        assert hasattr(control_panel_state, 'refresh_interval_seconds')
        
        logger.info("✅ PASSED: Control panel state integration verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Control panel integration error - {e}")
        return False

def test_key_levels_data_structure():
    """Test 4: Verify key levels data structure and access pattern"""
    logger.info("🧪 TEST 4: Key Levels Data Structure")
    
    try:
        from data_models import KeyLevelsDataV2_5, KeyLevelV2_5
        
        # Create test key level
        test_key_level = KeyLevelV2_5(
            level_price=4500.0,
            level_type="Support",
            conviction_score=0.85,
            contributing_metrics=["a_mspi", "nvp"],
            source_identifier="test_source"
        )
        
        # Create test key levels data
        key_levels_data = KeyLevelsDataV2_5(
            supports=[test_key_level],
            resistances=[],
            pin_zones=[],
            vol_triggers=[],
            major_walls=[],
            timestamp=datetime.now()
        )
        
        # Verify structure matches Structure Mode expectations
        assert hasattr(key_levels_data, 'supports')
        assert hasattr(key_levels_data, 'resistances')
        assert hasattr(key_levels_data, 'pin_zones')
        assert hasattr(key_levels_data, 'vol_triggers')
        assert hasattr(key_levels_data, 'major_walls')
        
        # Test flattening logic (as used in Structure Mode)
        all_levels = []
        for attr in ['supports', 'resistances', 'pin_zones', 'vol_triggers', 'major_walls']:
            levels = getattr(key_levels_data, attr, [])
            for lvl in levels:
                d = lvl.model_dump()
                d['level_type'] = d.get('level_type', attr[:-1].capitalize())
                all_levels.append(d)
        
        assert len(all_levels) == 1
        assert all_levels[0]['level_price'] == 4500.0
        assert all_levels[0]['conviction_score'] == 0.85
        
        logger.info("✅ PASSED: Key levels data structure verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Key levels data structure error - {e}")
        return False

def test_strike_level_metrics_access():
    """Test 5: Verify strike-level metrics access patterns"""
    logger.info("🧪 TEST 5: Strike-Level Metrics Access Patterns")
    
    try:
        from data_models import ProcessedStrikeLevelMetricsV2_5
        
        # Create test strike-level metrics
        test_strike_metrics = ProcessedStrikeLevelMetricsV2_5(
            strike=4500.0,
            a_dag_strike=1500.0,
            e_sdag_mult_strike=2000.0,
            e_sdag_dir_strike=1800.0,
            e_sdag_w_strike=2200.0,
            e_sdag_vf_strike=1900.0,
            a_mspi_strike=0.75,
            total_gxoi_at_strike=50000.0,
            total_dxoi_at_strike=25000.0,
            prediction_confidence=0.8,
            signal_strength=0.7
        )
        
        # Test DataFrame conversion (as used in Structure Mode)
        df_strike = pd.DataFrame([test_strike_metrics.model_dump()])
        
        # Verify required columns exist
        required_columns = [
            'strike', 'a_dag_strike', 'e_sdag_mult_strike', 'e_sdag_dir_strike',
            'e_sdag_w_strike', 'e_sdag_vf_strike', 'a_mspi_strike'
        ]
        
        missing_columns = []
        for col in required_columns:
            if col not in df_strike.columns:
                missing_columns.append(col)
        
        if missing_columns:
            logger.error(f"❌ FAILED: Missing required columns: {missing_columns}")
            return False
        
        # Test E-SDAG components (as used in Structure Mode)
        components = [
            ("Multiplicative", 'e_sdag_mult_strike'),
            ("Directional", 'e_sdag_dir_strike'),
            ("Weighted", 'e_sdag_w_strike'),
            ("Vol Flow", 'e_sdag_vf_strike')
        ]
        
        for name, column in components:
            if column not in df_strike.columns:
                logger.error(f"❌ FAILED: Missing E-SDAG component: {column}")
                return False
            
            value = df_strike[column].iloc[0]
            if pd.isna(value):
                logger.warning(f"⚠️ WARNING: E-SDAG component {name} has NaN value")
        
        logger.info("✅ PASSED: Strike-level metrics access patterns verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Strike-level metrics access error - {e}")
        return False

def test_data_pipeline_integrity():
    """Test 6: Verify data pipeline from orchestrator to Structure Mode"""
    logger.info("🧪 TEST 6: Data Pipeline Integrity")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from data_models import FinalAnalysisBundleV2_5
        
        # Verify orchestrator key levels generation method exists
        orchestrator_methods = [
            '_generate_key_levels',
            '_retrieve_key_levels_from_database'
        ]
        
        for method in orchestrator_methods:
            if not hasattr(ITSOrchestratorV2_5, method):
                logger.error(f"❌ FAILED: Missing orchestrator method: {method}")
                return False
        
        # Verify FinalAnalysisBundleV2_5 has required attributes
        required_bundle_attrs = [
            'processed_data_bundle',
            'key_levels_data_v2_5',
            'target_symbol',
            'bundle_timestamp'
        ]
        
        # Check if attributes exist in the model
        from data_models.core_models import FinalAnalysisBundleV2_5
        model_fields = FinalAnalysisBundleV2_5.model_fields.keys()
        
        missing_attrs = []
        for attr in required_bundle_attrs:
            if attr not in model_fields:
                missing_attrs.append(attr)
        
        if missing_attrs:
            logger.error(f"❌ FAILED: Missing bundle attributes: {missing_attrs}")
            return False
        
        logger.info("✅ PASSED: Data pipeline integrity verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Data pipeline integrity error - {e}")
        return False

def test_zero_tolerance_compliance():
    """Test 7: Verify zero tolerance for fake data policy compliance"""
    logger.info("🧪 TEST 7: Zero Tolerance for Fake Data Compliance")
    
    try:
        # Check Structure Mode for prohibited patterns
        with open('dashboard_application/modes/structure_mode_display_v2_5.py', 'r', encoding='utf-8') as f:
            structure_mode_code = f.read()
        
        # Check for prohibited fake data patterns
        prohibited_patterns = [
            'or 0.0',
            'or 0',
            'if not.*else 0',
            'default=0.0',
            'fillna(0)',
            'placeholder'
        ]
        
        violations = []
        for pattern in prohibited_patterns:
            if pattern in structure_mode_code:
                violations.append(pattern)
        
        if violations:
            logger.warning(f"⚠️ WARNING: Potential fake data patterns found: {violations}")
            # Don't fail the test, just warn as some patterns might be legitimate
        
        # Check for proper error handling
        required_error_patterns = [
            'logger.warning',
            'logger.error',
            'Strike level data not available',
            'No key levels identified'
        ]
        
        missing_error_handling = []
        for pattern in required_error_patterns:
            if pattern not in structure_mode_code:
                missing_error_handling.append(pattern)
        
        if missing_error_handling:
            logger.error(f"❌ FAILED: Missing error handling patterns: {missing_error_handling}")
            return False
        
        logger.info("✅ PASSED: Zero tolerance compliance verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Zero tolerance compliance check error - {e}")
        return False

def main():
    """Run comprehensive Structure Mode audit"""
    logger.info("🎯 STARTING COMPREHENSIVE STRUCTURE MODE AUDIT")
    logger.info("=" * 80)
    
    tests = [
        test_structure_mode_imports,
        test_structure_mode_chart_functions,
        test_control_panel_integration,
        test_key_levels_data_structure,
        test_strike_level_metrics_access,
        test_data_pipeline_integrity,
        test_zero_tolerance_compliance
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            logger.error(f"❌ CRITICAL ERROR in {test.__name__}: {e}")
            results.append(False)
        
        logger.info("-" * 40)
    
    # Summary
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    logger.info("=" * 80)
    logger.info(f"🎯 STRUCTURE MODE AUDIT: {passed}/{total} tests passed ({success_rate:.1f}%)")
    
    if success_rate == 100:
        logger.info("🎉 SUCCESS: Structure Mode audit completed successfully!")
        logger.info("✅ All components verified for data accuracy and integrity")
        return True
    else:
        logger.error("❌ ISSUES DETECTED: Some aspects need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
