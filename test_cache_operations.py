#!/usr/bin/env python3
"""
Test script to debug cache operations
"""
import sys
sys.path.append('.')

from data_management.enhanced_cache_manager_v2_5 import EnhancedCacheManagerV2_5, CacheLevel

def test_cache_operations():
    try:
        print("🔍 Testing cache operations...")
        
        # Initialize cache manager
        cache_manager = EnhancedCacheManagerV2_5(
            cache_root="cache/test",
            memory_limit_mb=10,
            disk_limit_mb=50,
            default_ttl_seconds=3600
        )
        
        # Test put operation
        print("🔍 Testing put operation...")
        success = cache_manager.put("SPX", "test_metric", [1.0, 2.0, 3.0], cache_level=CacheLevel.MEMORY)
        print(f"✅ Put operation success: {success}")
        
        # Test get operation
        print("🔍 Testing get operation...")
        result = cache_manager.get("SPX", "test_metric")
        print(f"✅ Get operation result: {result}")
        
        # Test get operation with non-existent key
        print("🔍 Testing get operation with non-existent key...")
        result_none = cache_manager.get("SPX", "non_existent_metric")
        print(f"✅ Get operation result (should be None): {result_none}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_cache_operations()
    if success:
        print("🎉 Cache operations test completed successfully")
    else:
        print("💥 Cache operations test failed")
