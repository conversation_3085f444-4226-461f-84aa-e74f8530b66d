#!/usr/bin/env python3
"""
Test script to check what volatility data ConvexValue provides for underlying assets.
Tests the "get" endpoint (underlying) vs "get/chain" endpoint (options).
"""

import os
import sys
from dotenv import load_dotenv

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

try:
    from convexlib.api import ConvexApi
except ImportError:
    print("❌ ConvexApi not available. Install with: pip install convexlib")
    sys.exit(1)

def test_convex_underlying_volatility():
    """Test what volatility data ConvexValue provides for underlying assets."""
    
    email = os.getenv("CONVEX_EMAIL")
    password = os.getenv("CONVEX_PASSWORD")
    
    if not email or not password:
        print("❌ CONVEX_EMAIL or CONVEX_PASSWORD not found in environment")
        return
    
    try:
        # Initialize ConvexValue API
        print("🔌 Connecting to ConvexValue API...")
        convex_api = ConvexApi(email, password, "pro")
        print("✅ Connected successfully")
        
        # Test symbol
        symbol = "SPX"
        
        # Test 1: Get underlying data using "get" endpoint
        print(f"\n📊 Testing underlying data for {symbol} using 'get' endpoint...")
        
        # Parameters we're requesting for underlying
        underlying_params = [
            "price", "volatility", "day_volume", "call_gxoi", "put_gxoi",
            "gammas_call_buy", "gammas_call_sell", "gammas_put_buy", "gammas_put_sell",
            "deltas_call_buy", "deltas_call_sell", "deltas_put_buy", "deltas_put_sell",
            "vegas_call_buy", "vegas_call_sell", "vegas_put_buy", "vegas_put_sell",
            "thetas_call_buy", "thetas_call_sell", "thetas_put_buy", "thetas_put_sell",
            "call_vxoi", "put_vxoi", "value_bs", "volm_bs", "deltas_buy", "deltas_sell",
            "vegas_buy", "vegas_sell", "thetas_buy", "thetas_sell", "volm_call_buy",
            "volm_put_buy", "volm_call_sell", "volm_put_sell", "value_call_buy",
            "value_put_buy", "value_call_sell", "value_put_sell", "vflowratio",
            "dxoi", "gxoi", "vxoi", "txoi", "call_dxoi", "put_dxoi"
        ]
        
        # Make the API call using the correct method
        response = convex_api.get_und(symbols=[symbol], params=underlying_params)
        
        print(f"📋 Raw response type: {type(response)}")
        print(f"📋 Raw response: {response}")
        
        if response and 'data' in response:
            data = response['data']
            print(f"\n📊 Data length: {len(data)}")

            if data and len(data) > 0:
                # FIXED: Use correct parsing logic like the fetcher
                row = data[0]  # First row
                if row and len(row) > 0:
                    symbol_data = row[0] if isinstance(row[0], list) else row  # Extract actual data array
                    print(f"📊 Symbol data length: {len(symbol_data)}")
                    print(f"📊 Symbol: {symbol_data[0] if len(symbol_data) > 0 else 'N/A'}")

                    # Check each parameter
                    print(f"\n🔍 Parameter Analysis:")
                    for i, param in enumerate(underlying_params):
                        if i + 1 < len(symbol_data):
                            value = symbol_data[i + 1]
                            print(f"  {param}: {value} (type: {type(value)})")

                            # Special focus on volatility
                            if param == "volatility":
                                if value is None:
                                    print(f"  ❌ CRITICAL: {param} is None - NO REAL VOLATILITY DATA!")
                                elif value == 0.0:
                                    print(f"  ⚠️  WARNING: {param} is 0.0 - might be fake/missing data")
                                else:
                                    print(f"  ✅ {param} has real data: {value}")
                        else:
                            print(f"  {param}: MISSING (index {i+1} >= data length {len(symbol_data)})")

                            # Critical check for volatility
                            if param == "volatility":
                                print(f"  ❌ CRITICAL: volatility parameter is MISSING from ConvexValue response!")
                else:
                    print("❌ No row data found")
            else:
                print("❌ No data returned for symbol")
        else:
            print("❌ No 'data' key in response or response is empty")
            
        # Test 2: Check if volatility is available in options chain data
        print(f"\n📊 Testing if volatility is in options chain data...")
        chain_response = convex_api.get_chain_as_rows(symbol, params=["price", "volatility"], exps=[1, 2, 3], rng=0.05)  # 3 expirations, 5% range
        
        if chain_response and 'data' in chain_response:
            chain_data = chain_response['data']
            if chain_data and len(chain_data) > 0:
                first_contract = chain_data[0]
                print(f"📊 First options contract data length: {len(first_contract)}")
                
                # Check if volatility is in options data
                options_params = [
                    "price", "volatility", "multiplier", "oi", "delta", "gamma", "theta", "vega",
                    "vanna", "vomma", "charm", "dxoi", "gxoi", "vxoi", "txoi", "vannaxoi", "vommaxoi", "charmxoi"
                ]
                
                print(f"🔍 Options Contract Volatility Check:")
                for i, param in enumerate(options_params):
                    if i + 1 < len(first_contract):
                        value = first_contract[i + 1]
                        if param == "volatility":
                            print(f"  Options {param}: {value} (type: {type(value)})")
                            if value is not None and value != 0.0:
                                print(f"  ✅ Options contracts DO have volatility data!")
                            break
        
    except Exception as e:
        print(f"❌ Error testing ConvexValue volatility: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_convex_underlying_volatility()
