"""
Pydantic models defining contextual information used across the EOTS v2.5 system.
These schemas help in tailoring analysis based on ticker-specific characteristics,
market events, or time-based conditions.
"""
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, field_validator, FieldValidationInfo
from typing import Optional, Dict, Any
from .context_config_schemas import AdvancedOptionsMetrics


class MarketRegimeState(str, Enum):
    """Market regime states for the expert system.
    
    Attributes:
        BULLISH_TREND: Sustained upward price movement
        BEARISH_TREND: Sustained downward price movement
        SIDEWAYS: No clear trend, price oscillating in a range
        VOLATILITY_EXPANSION: Increasing price volatility
        VOLATILITY_CONTRACTION: Decreasing price volatility
        BULLISH_REVERSAL: Potential reversal from downtrend to uptrend
        BEARISH_REVERSAL: Potential reversal from uptrend to downtrend
        DISTRIBUTION: Smart money distributing positions
        ACCUMULATION: Smart money accumulating positions
        CAPITULATION: Panic selling
        EUPHORIA: Extreme bullish sentiment
        PANIC: Extreme bearish sentiment
        CONSOLIDATION: Price moving in a tight range
        BREAKOUT: Price breaking out of a range
        BREAKDOWN: Price breaking down from a range
        CHOPPY: Erratic price action
        TRENDING: Clear directional movement
        RANGE_BOUND: Price contained within support/resistance
        UNDEFINED: Default/unknown state
    """
    BULLISH_TREND = "bullish_trend"
    BEARISH_TREND = "bearish_trend"
    SIDEWAYS = "sideways"
    VOLATILITY_EXPANSION = "volatility_expansion"
    VOLATILITY_CONTRACTION = "volatility_contraction"
    BULLISH_REVERSAL = "bullish_reversal"
    BEARISH_REVERSAL = "bearish_reversal"
    DISTRIBUTION = "distribution"
    ACCUMULATION = "accumulation"
    CAPITULATION = "capitulation"
    EUPHORIA = "euphoria"
    PANIC = "panic"
    CONSOLIDATION = "consolidation"
    BREAKOUT = "breakout"
    BREAKDOWN = "breakdown"
    CHOPPY = "choppy"
    TRENDING = "trending"
    RANGE_BOUND = "range_bound"
    UNDEFINED = "undefined"

class TickerContextDictV2_5(BaseModel):
    """
    Holds various contextual flags and states specific to the ticker being analyzed,
    generated by TickerContextAnalyzerV2_5. This information is used by other
    system components (MRE, Metrics Calculator, ATIF) to adapt their logic.
    """
    is_0dte: Optional[bool] = Field(None, description="True if current day is a 0 DTE (Days To Expiration) day for the active symbol.")
    is_1dte: Optional[bool] = Field(None, description="True if current day is a 1 DTE day for the active symbol.")
    is_spx_mwf_expiry_type: Optional[bool] = Field(None, description="Flags specific SPX Monday/Wednesday/Friday expiration types.")
    is_spy_eom_expiry: Optional[bool] = Field(None, description="Flags if it's an SPY End-of-Month expiration.")
    is_quad_witching_week_flag: Optional[bool] = Field(None, description="Flags if the current week is a quadruple witching week.")
    days_to_nearest_0dte: Optional[int] = Field(None, description="Number of calendar days to the nearest 0DTE event for the symbol.")
    days_to_monthly_opex: Optional[int] = Field(None, description="Number of calendar days to the next standard monthly options expiration.")

    # Event-based context
    is_fomc_meeting_day: Optional[bool] = Field(None, description="True if the current day is an FOMC meeting day.")
    is_fomc_announcement_imminent: Optional[bool] = Field(None, description="True if an FOMC announcement is expected shortly (e.g., within a specific time window).")
    post_fomc_drift_period_active: Optional[bool] = Field(None, description="True if within the typical post-FOMC announcement drift period.")

    # Behavioral pattern flags (examples)
    vix_spy_price_divergence_strong_negative: Optional[bool] = Field(None, description="Example: True if VIX is up strongly while SPY is also up, indicating unusual divergence.")
    
    # Intraday session context
    active_intraday_session: Optional[str] = Field(None, description="Current intraday session (e.g., 'OPENING_RUSH', 'LUNCH_LULL', 'POWER_HOUR', 'EOD_AUCTION').")
    is_near_auction_period: Optional[bool] = Field(None, description="True if current time is near market open or close auction periods.")

    # General ticker characteristics
    ticker_liquidity_profile_flag: Optional[str] = Field(None, description="General liquidity assessment for the ticker (e.g., 'High', 'Medium', 'Low', 'Illiquid').")
    ticker_volatility_state_flag: Optional[str] = Field(None, description="Assessment of the ticker's current volatility character (e.g., 'IV_HIGH_RV_LOW', 'IV_CRUSH_IMMINENT').")
    
    # Earnings context (for equities)
    earnings_approaching_flag: Optional[bool] = Field(None, description="True if an earnings announcement is imminent for the stock (e.g., within a week).")
    days_to_earnings: Optional[int] = Field(None, description="Number of calendar days to the next scheduled earnings announcement.")

    # Fields from old schema
    is_SPX_0DTE_Friday_EOD: Optional[bool] = None
    a_mspi_und_summary_score: Optional[float] = None
    nvp_by_strike: Optional[Dict[str, float]] = None
    hp_eod_und: Optional[float] = None
    trend_threshold: Optional[float] = None
    advanced_options_metrics: Optional[AdvancedOptionsMetrics] = None

    class Config:
        extra = 'forbid' # Internal model, structure should be strictly defined


class TimeOfDayDefinitions(BaseModel):
    """
    Defines critical time points for market operations and EOTS v2.5 internal logic,
    such as determining intraday sessions or when to perform EOD calculations.
    These are typically loaded from system configuration.
    """
    market_open: str = Field(default="09:30:00", description="Market open time in HH:MM:SS format (e.g., '09:30:00').")
    market_close: str = Field(default="16:00:00", description="Market close time in HH:MM:SS format (e.g., '16:00:00').")
    pre_market_start: str = Field(default="04:00:00", description="Pre-market start time in HH:MM:SS format.")
    after_hours_end: str = Field(default="20:00:00", description="After hours end time in HH:MM:SS format.")
    eod_pressure_calc_time: str = Field(default="15:00:00", description="Time to trigger end-of-day pressure calculations (e.g., HP_EOD) in HH:MM:SS format.")
    # Add other specific time definitions as needed, e.g., for intraday session boundaries
    # opening_rush_end: str = Field(default="10:15:00", description="End of 'Opening Rush' session.")
    # lunch_lull_start: str = Field(default="12:00:00", description="Start of 'Lunch Lull' session.")
    # lunch_lull_end: str = Field(default="13:30:00", description="End of 'Lunch Lull' session.")
    # power_hour_start: str = Field(default="15:00:00", description="Start of 'Power Hour' session.")

    class Config:
        extra = 'forbid' # Configuration model, structure should be strictly defined


# --- PORTED: DynamicThresholdsV2_5 (from deprecated_files/eots_schemas_v2_5.py) ---
class DynamicThresholdsV2_5(BaseModel):
    """Dynamic thresholds for market regime detection and signal generation.
    This model defines configurable thresholds used throughout the system for:
    - Market regime classification
    - Signal generation and filtering
    - Risk management
    - Data quality assessment
    All thresholds are designed to be dynamically adjustable based on market conditions.
    """
    vapi_fa_bullish_thresh: float = Field(default=1.5, gt=0.0, description="Z-score threshold for bullish VAPI-FA signals")
    vapi_fa_bearish_thresh: float = Field(default=-1.5, lt=0.0, description="Z-score threshold for bearish VAPI-FA signals")
    vri_bullish_thresh: float = Field(default=0.6, gt=0.0, lt=1.0, description="VRI threshold for bullish regime classification")
    vri_bearish_thresh: float = Field(default=-0.6, gt=-1.0, lt=0.0, description="VRI threshold for bearish regime classification")
    negative_thresh_default: float = Field(default=-0.5, lt=0.0, description="Default negative threshold for signal classification")
    positive_thresh_default: float = Field(default=0.5, gt=0.0, description="Default positive threshold for signal classification")
    significant_pos_thresh: float = Field(default=1000.0, gt=0, description="Threshold for significant positive values (e.g., OI, volume)")
    significant_neg_thresh: float = Field(default=-1000.0, lt=0, description="Threshold for significant negative values (e.g., net flows)")
    mid_high_nvp_thresh_pos: float = Field(default=5000.0, gt=0, description="Mid-high threshold for Net Vega Position")
    high_nvp_thresh_pos: float = Field(default=10000.0, gt=0, description="High threshold for Net Vega Position")
    dwfd_strong_thresh: float = Field(default=1.5, gt=0.0, description="Threshold for strong DWFD (Daily/Weekly Flow Divergence) signal")
    tw_laf_strong_thresh: float = Field(default=1.2, gt=0.0, description="Threshold for strong TW-LAF (Time-Weighted Liquidity-Adjusted Flow) signal")
    volatility_expansion_thresh: float = Field(default=0.8, gt=0.0, le=1.0, description="Threshold for volatility expansion detection (0-1 scale)")
    hedging_pressure_thresh: float = Field(default=500.0, gt=0, description="Threshold for significant hedging pressure")
    high_confidence_thresh: float = Field(default=0.8, gt=0.0, le=1.0, description="Minimum score for high confidence classification (0-1 scale)")
    moderate_confidence_thresh: float = Field(default=0.6, gt=0.0, le=1.0, description="Minimum score for moderate confidence classification (0-1 scale)")
    data_quality_thresh: float = Field(default=0.7, gt=0.0, le=1.0, description="Minimum data quality score for analytics (0-1 scale)")

    model_config = ConfigDict(extra='forbid')

    @field_validator('vri_bullish_thresh', 'vri_bearish_thresh', mode='before')
    @classmethod
    def validate_vri_thresholds(cls, v: float, info: FieldValidationInfo) -> float:
        if info.field_name == 'vri_bullish_thresh' and not (0.0 < v < 1.0):
            raise ValueError('vri_bullish_thresh must be between 0 and 1')
        if info.field_name == 'vri_bearish_thresh' and not (-1.0 < v < 0.0):
            raise ValueError('vri_bearish_thresh must be between -1 and 0')
        return v

    @field_validator('high_confidence_thresh', 'moderate_confidence_thresh', 'data_quality_thresh', mode='before')
    @classmethod
    def validate_probability_thresholds(cls, v: float) -> float:
        if not (0.0 < v <= 1.0):
            raise ValueError('Thresholds must be between 0 and 1')
        return v

# --- END PORT ---
