# core_analytics_engine/eots_metrics/adaptive_calculator.py

"""
EOTS Adaptive Calculator - Consolidated Adaptive Metrics with Regime Detection

Consolidates:
- adaptive_metrics.py: Tier 2 adaptive metrics (A-DAG, E-SDAG, D-TDPI, VRI 2.0)
- elite_regime_detector.py: Market regime classification
- elite_volatility_surface.py: Volatility surface analysis

Optimizations:
- Unified regime detection logic
- Streamlined adaptive calculations
- Integrated volatility surface analysis
- Eliminated duplicate context determination
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Union, Optional
from enum import Enum
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from functools import lru_cache

from core_analytics_engine.eots_metrics.core_calculator import CoreCalculator
from data_management.enhanced_cache_manager_v2_5 import EnhancedCacheManagerV2_5

logger = logging.getLogger(__name__)
EPSILON = 1e-9

class MarketRegime(Enum):
    """Consolidated market regime classifications"""
    LOW_VOL_TRENDING = "low_vol_trending"
    LOW_VOL_RANGING = "low_vol_ranging"
    MEDIUM_VOL_TRENDING = "medium_vol_trending"
    MEDIUM_VOL_RANGING = "medium_vol_ranging"
    HIGH_VOL_TRENDING = "high_vol_trending"
    HIGH_VOL_RANGING = "high_vol_ranging"
    STRESS_REGIME = "stress_regime"
    EXPIRATION_REGIME = "expiration_regime"
    REGIME_UNCLEAR_OR_TRANSITIONING = "regime_unclear_or_transitioning"

class AdaptiveCalculator(CoreCalculator):
    """
    Consolidated adaptive calculator with regime detection and volatility surface analysis.
    
    Combines functionality from:
    - AdaptiveMetricsCalculator: A-DAG, E-SDAG, D-TDPI, VRI 2.0, concentration indices
    - EliteMarketRegimeDetector: Market regime classification
    - EliteVolatilitySurface: Volatility surface modeling
    """
    
    def __init__(self, config_manager: Any, historical_data_manager: Any, enhanced_cache_manager: EnhancedCacheManagerV2_5, elite_config: Any = None):
        super().__init__(config_manager, historical_data_manager, enhanced_cache_manager)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.elite_config = elite_config or {}
        
        # Initialize regime detection components
        self.regime_model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.regime_scaler = StandardScaler()
        self.is_regime_model_trained = False
        
        # Volatility surface cache
        self.surface_cache = {}
        
        # Adaptive calculation thresholds (optimized from original modules)
        self.VOLATILITY_THRESHOLDS = {'low': 0.15, 'high': 0.30}
        self.DTE_THRESHOLDS = {'short': 7, 'medium': 30, 'long': 60}
        self.CONCENTRATION_WINDOW = 20
    
    # =============================================================================
    # ADAPTIVE METRICS (Tier 2) - Consolidated from adaptive_metrics.py
    # =============================================================================
    
    def calculate_all_adaptive_metrics(self, df_strike: pd.DataFrame, und_data) -> pd.DataFrame:
        """
        Orchestrates calculation of all adaptive metrics using proper Pydantic v2 architecture.

        Calculates:
        - Market regime classification
        - Volatility regime determination
        - A-DAG (Adaptive Delta Adjusted Gamma Exposure)
        - E-SDAG (Enhanced Skew and Delta Adjusted Gamma Exposure)
        - D-TDPI (Dynamic Time Decay Pressure Indicator)
        - VRI 2.0 (Volatility Regime Indicator)
        - Concentration indices (GCI, DCI)
        - 0DTE suite metrics

        Args:
            df_strike: DataFrame with strike-level data
            und_data: Pydantic model (ProcessedUnderlyingAggregatesV2_5) - accessed via dot notation

        Returns:
            pd.DataFrame: Updated strike-level data with adaptive metrics
        """
        if df_strike.empty:
            return df_strike

        self.logger.debug("Calculating adaptive metrics...")

        try:
            # Determine market context (consolidated regime detection) using proper Pydantic access
            current_market_regime = self._determine_market_regime_optimized(und_data, df_strike)

            # Determine volatility regime using proper Pydantic access
            volatility_regime = self._determine_volatility_regime_optimized(und_data, df_strike)

            # Get unified context for adaptive calculations using proper Pydantic access
            volatility_context = self._get_volatility_context_optimized(und_data)
            dte_context = self._get_average_dte_context_optimized(df_strike)

            # Calculate adaptive metrics using proper Pydantic access
            df_strike = self._calculate_a_dag_optimized(df_strike, und_data, current_market_regime, volatility_context, dte_context)
            df_strike = self._calculate_e_sdag_optimized(df_strike, und_data, current_market_regime, volatility_context, dte_context)
            df_strike = self._calculate_d_tdpi_optimized(df_strike, und_data, current_market_regime, volatility_context, dte_context)
            df_strike = self._calculate_vri_2_0_optimized(df_strike, und_data, current_market_regime, volatility_context, dte_context)
            df_strike = self._calculate_concentration_indices_optimized(df_strike, und_data)
            df_strike = self._calculate_0dte_suite_optimized(df_strike, und_data, dte_context)

            self.logger.debug(f"Adaptive metrics calculation complete for {len(df_strike)} strikes.")
            return df_strike

        except Exception as e:
            self.logger.error(f"Error calculating adaptive metrics: {e}", exc_info=True)
            return df_strike
    
    # =============================================================================
    # REGIME DETECTION - Consolidated from elite_regime_detector.py
    # =============================================================================
    
    def _determine_market_regime_optimized(self, und_data, df_strike: pd.DataFrame) -> MarketRegime:
        """Optimized market regime detection using simplified heuristics with proper Pydantic access"""
        try:
            # Extract key market indicators using proper Pydantic attribute access
            # FAIL-FAST: Require real market data - no fake defaults allowed
            current_iv = self._safe_float(getattr(und_data, 'u_volatility', None), 'u_volatility')
            price_change_pct = self._safe_float(getattr(und_data, 'price_change_pct_und', None), 'price_change_pct_und')
            volume = self._safe_float(getattr(und_data, 'day_volume', None), 'day_volume')
            
            # Volatility classification
            if current_iv > self.VOLATILITY_THRESHOLDS['high']:
                vol_regime = 'HIGH_VOL'
            elif current_iv < self.VOLATILITY_THRESHOLDS['low']:
                vol_regime = 'LOW_VOL'
            else:
                vol_regime = 'MEDIUM_VOL'
            
            # Trend classification (simplified)
            if abs(price_change_pct) > 0.02:  # 2% threshold
                trend_regime = 'TRENDING'
            else:
                trend_regime = 'RANGING'
            
            # Special regime detection
            if current_iv > 0.50 and volume > 50000000:  # Stress conditions
                return MarketRegime.STRESS_REGIME
            
            # Check for expiration regime (simplified)
            if self._is_expiration_week(df_strike):
                return MarketRegime.EXPIRATION_REGIME
            
            # Combine volatility and trend regimes
            regime_name = f"{vol_regime}_{trend_regime}"
            
            try:
                return MarketRegime(regime_name.lower())
            except ValueError:
                return MarketRegime.REGIME_UNCLEAR_OR_TRANSITIONING
                
        except Exception as e:
            self.logger.warning(f"Error determining market regime: {e}")
            return MarketRegime.REGIME_UNCLEAR_OR_TRANSITIONING
    
    def _is_expiration_week(self, df_strike: pd.DataFrame) -> bool:
        """Check if current week contains major expiration"""
        try:
            if 'dte' in df_strike.columns:
                min_dte = df_strike['dte'].min()
                return min_dte <= 7  # Within a week of expiration
            return False
        except:
            return False
    
    # =============================================================================
    # VOLATILITY SURFACE ANALYSIS - Consolidated from elite_volatility_surface.py
    # =============================================================================
    
    def _determine_volatility_regime_optimized(self, und_data, df_strike: pd.DataFrame) -> str:
        """Optimized volatility regime determination with proper Pydantic access"""
        try:
            # FAIL-FAST: Require real volatility data - no fake defaults allowed
            current_iv = self._safe_float(getattr(und_data, 'u_volatility', None), 'u_volatility')
            
            # Calculate volatility surface metrics if strike data available
            if not df_strike.empty and 'implied_volatility' in df_strike.columns:
                iv_series = pd.to_numeric(df_strike['implied_volatility'], errors='coerce').dropna()
                if len(iv_series) > 0:
                    iv_mean = iv_series.mean()
                    iv_std = iv_series.std()
                    
                    # Surface stability check
                    surface_stability = 1.0 - min(iv_std / max(iv_mean, 0.01), 1.0)
                    
                    if surface_stability < 0.15:  # Unstable surface
                        return "unstable"
                    elif iv_mean > 0.40:
                        return "high_vol"
                    elif iv_mean < 0.15:
                        return "low_vol"
                    else:
                        return "normal"
            
            # Fallback to underlying IV
            if current_iv > 0.40:
                return "high_vol"
            elif current_iv < 0.15:
                return "low_vol"
            else:
                return "normal"
                
        except Exception as e:
            self.logger.warning(f"Error determining volatility regime: {e}")
            return "normal"
    
    @lru_cache(maxsize=64)
    def _calculate_skew_adjustment_optimized(self, strike: float, atm_vol: float, strike_vol: float, alpha: float = 1.0) -> float:
        """Optimized skew adjustment calculation with caching"""
        if atm_vol <= 0 or strike_vol <= 0:
            return 1.0
        
        skew_ratio = strike_vol / atm_vol
        adjustment = 1.0 + alpha * (skew_ratio - 1.0)
        return max(0.1, min(3.0, adjustment))  # Bounded adjustment
    
    # =============================================================================
    # UTILITY METHODS
    # =============================================================================

    def _get_column_required(self, df: pd.DataFrame, column: str, context: str = "") -> pd.Series:
        """Get required column from DataFrame - FAIL FAST if missing (NO FAKE DATA ALLOWED)"""
        if df is None:
            raise ValueError(f"CRITICAL: DataFrame is None when accessing required field '{column}' {context}")

        if column not in df.columns:
            available_columns = list(df.columns)
            raise ValueError(
                f"CRITICAL: Required field '{column}' missing from data pipeline {context}. "
                f"Available columns: {available_columns}. "
                f"This indicates a data processing failure - NO FAKE DATA WILL BE SUBSTITUTED!"
            )

        series = df[column]
        if series.isna().all():
            raise ValueError(
                f"CRITICAL: Required field '{column}' contains only NaN values {context}. "
                f"This indicates corrupted data - NO FAKE DATA WILL BE SUBSTITUTED!"
            )

        # Return series with NaN values intact - let downstream handle appropriately
        return series

    # =============================================================================
    # ADAPTIVE METRIC CALCULATIONS - Optimized from adaptive_metrics.py
    # =============================================================================
    
    def _calculate_a_dag_optimized(self, df_strike: pd.DataFrame, und_data,
                                  market_regime: MarketRegime, volatility_context: str, dte_context: str) -> pd.DataFrame:
        """Optimized A-DAG calculation"""
        try:
            # Get base gamma exposure using FAIL-FAST validation (NO FAKE DATA)
            gamma_exposure = self._get_column_required(df_strike, 'total_gxoi_at_strike', 'for A-DAG calculation')
            delta_exposure = self._get_column_required(df_strike, 'total_dxoi_at_strike', 'for A-DAG calculation')
            
            # Adaptive alpha based on regime
            regime_multipliers = {
                MarketRegime.HIGH_VOL_TRENDING: 1.5,
                MarketRegime.HIGH_VOL_RANGING: 1.2,
                MarketRegime.STRESS_REGIME: 2.0,
                MarketRegime.EXPIRATION_REGIME: 0.8
            }
            adaptive_alpha = regime_multipliers.get(market_regime, 1.0)
            
            # Flow alignment factor - Use proper Pydantic model access
            # FAIL-FAST: Require real gamma flow data - no fake defaults allowed
            net_gamma_flow = self._safe_float(getattr(und_data, 'net_cust_gamma_flow_und', None), 'net_cust_gamma_flow_und')
            flow_alignment = np.sign(net_gamma_flow) if abs(net_gamma_flow) > 1000 else 0
            
            # Calculate A-DAG score
            a_dag_exposure = gamma_exposure * adaptive_alpha
            a_dag_score = a_dag_exposure * (1 + flow_alignment * 0.2)

            # Store results with correct field names for ProcessedStrikeLevelMetricsV2_5
            df_strike['a_dag_exposure'] = a_dag_exposure
            df_strike['a_dag_adaptive_alpha'] = adaptive_alpha
            df_strike['a_dag_flow_alignment'] = flow_alignment
            df_strike['a_dag_strike'] = a_dag_score  # CRITICAL FIX: Use correct field name
            
            return df_strike
            
        except Exception as e:
            self.logger.warning(f"Error calculating A-DAG: {e}")
            df_strike['a_dag_strike'] = 0.0  # CRITICAL FIX: Use correct field name
            return df_strike
    
    def _calculate_e_sdag_optimized(self, df_strike: pd.DataFrame, und_data,
                                   market_regime: MarketRegime, volatility_context: str, dte_context: str) -> pd.DataFrame:
        """Optimized E-SDAG calculation"""
        try:
            # Get base exposures using FAIL-FAST validation (NO FAKE DATA)
            gamma_exposure = self._get_column_required(df_strike, 'total_gxoi_at_strike', 'for E-SDAG calculation')
            # FAIL-FAST: Require real underlying price - no fake defaults allowed
            underlying_price = self._safe_float(getattr(und_data, 'price', None), 'underlying_price')

            # Prevent division by zero
            if underlying_price <= 0:
                underlying_price = 100.0  # Default fallback price

            # Calculate skew adjustment for each strike
            strikes = self._get_column_required(df_strike, 'strike', 'for E-SDAG calculation')
            # FAIL-FAST: Require real volatility data - no fake defaults allowed
            current_iv = self._safe_float(getattr(und_data, 'u_volatility', None), 'u_volatility')

            skew_adjustments = []
            for strike in strikes:
                # Simplified skew calculation with safe division
                moneyness = strike / max(underlying_price, 0.01)  # Prevent division by zero
                if moneyness < 0.95:  # ITM puts
                    skew_adj = 1.1
                elif moneyness > 1.05:  # ITM calls
                    skew_adj = 0.9
                else:  # ATM
                    skew_adj = 1.0
                skew_adjustments.append(skew_adj)
            
            # E-SDAG calculation
            e_sdag_score = gamma_exposure * pd.Series(skew_adjustments)

            # Store results with correct field names for ProcessedStrikeLevelMetricsV2_5
            df_strike['e_sdag_mult_strike'] = e_sdag_score  # CRITICAL FIX: Use correct field name
            df_strike['e_sdag_skew_adj'] = skew_adjustments
            
            return df_strike
            
        except Exception as e:
            self.logger.warning(f"Error calculating E-SDAG: {e}")
            df_strike['e_sdag_mult_strike'] = 0.0  # CRITICAL FIX: Use correct field name
            return df_strike
    
    def _calculate_d_tdpi_optimized(self, df_strike: pd.DataFrame, und_data,
                                   market_regime: MarketRegime, volatility_context: str, dte_context: str) -> pd.DataFrame:
        """Optimized D-TDPI calculation"""
        try:
            # Get theta exposure using FAIL-FAST validation (NO FAKE DATA)
            theta_exposure = self._get_column_required(df_strike, 'total_txoi_at_strike', 'for ARFI calculation')

            # DTE-based scaling
            dte_values = self._get_column_required(df_strike, 'dte', 'for ARFI calculation')
            dte_scaling = np.where(dte_values <= 7, 2.0,  # 0DTE boost
                                 np.where(dte_values <= 30, 1.5, 1.0))  # Short-term boost
            
            # D-TDPI calculation
            d_tdpi_score = theta_exposure * dte_scaling

            # Store results with correct field names for ProcessedStrikeLevelMetricsV2_5
            df_strike['d_tdpi_strike'] = d_tdpi_score  # CRITICAL FIX: Use correct field name
            df_strike['d_tdpi_dte_scaling'] = dte_scaling
            
            return df_strike
            
        except Exception as e:
            self.logger.warning(f"Error calculating D-TDPI: {e}")
            df_strike['d_tdpi_strike'] = 0.0  # CRITICAL FIX: Use correct field name
            return df_strike
    
    def _calculate_vri_2_0_optimized(self, df_strike: pd.DataFrame, und_data,
                                    market_regime: MarketRegime, volatility_context: str, dte_context: str) -> pd.DataFrame:
        """Optimized VRI 2.0 calculation"""
        try:
            # Get vega exposure using FAIL-FAST validation (NO FAKE DATA)
            vega_exposure = self._get_column_required(df_strike, 'total_vxoi_at_strike', 'for VRI 2.0 calculation')
            
            # Volatility regime adjustment
            vol_multipliers = {'high_vol': 1.3, 'low_vol': 0.8, 'normal': 1.0}
            vol_multiplier = vol_multipliers.get(volatility_context, 1.0)
            
            # VRI 2.0 calculation
            vri_2_0_score = vega_exposure * vol_multiplier

            # Store results with correct field names for ProcessedStrikeLevelMetricsV2_5
            df_strike['vri_2_0_strike'] = vri_2_0_score  # CRITICAL FIX: Use correct field name
            df_strike['vri_2_0_vol_mult'] = vol_multiplier
            
            return df_strike
            
        except Exception as e:
            self.logger.warning(f"Error calculating VRI 2.0: {e}")
            df_strike['vri_2_0_strike'] = 0.0  # CRITICAL FIX: Use correct field name
            return df_strike
    
    def _calculate_concentration_indices_optimized(self, df_strike: pd.DataFrame, und_data) -> pd.DataFrame:
        """Optimized concentration indices calculation"""
        try:
            # Gamma Concentration Index (GCI) using FAIL-FAST validation (NO FAKE DATA)
            gamma_exposure = self._get_column_required(df_strike, 'total_gxoi_at_strike', 'for concentration metrics')
            total_gamma = gamma_exposure.sum()
            gci_score = gamma_exposure / max(abs(total_gamma), 1.0) if total_gamma != 0 else pd.Series([0] * len(df_strike))

            # Delta Concentration Index (DCI) using FAIL-FAST validation (NO FAKE DATA)
            delta_exposure = self._get_column_required(df_strike, 'total_dxoi_at_strike', 'for concentration metrics')
            total_delta = delta_exposure.sum()
            dci_score = delta_exposure / max(abs(total_delta), 1.0) if total_delta != 0 else pd.Series([0] * len(df_strike))
            
            # Store results
            df_strike['gci_score'] = gci_score
            df_strike['dci_score'] = dci_score
            
            return df_strike
            
        except Exception as e:
            self.logger.warning(f"Error calculating concentration indices: {e}")
            df_strike['gci_score'] = 0.0
            df_strike['dci_score'] = 0.0
            return df_strike
    
    def _calculate_0dte_suite_optimized(self, df_strike: pd.DataFrame, und_data, dte_context: str) -> pd.DataFrame:
        """Optimized 0DTE suite calculation"""
        try:
            # Filter for 0DTE options using FAIL-FAST validation (NO FAKE DATA)
            dte_values = self._get_column_required(df_strike, 'dte', 'for 0DTE suite calculation')

            # DEBUG: Log DTE values to understand the data
            print(f"DEBUG: DTE values range: min={dte_values.min():.2f}, max={dte_values.max():.2f}, count={len(dte_values)}")
            print(f"DEBUG: DTE values <= 1: {(dte_values <= 1).sum()}")
            print(f"DEBUG: DTE values <= 2: {(dte_values <= 2).sum()}")

            # Use more inclusive threshold for 0DTE detection (up to 2 days)
            is_0dte = dte_values <= 2

            if not is_0dte.any():
                # No 0DTE options - FAIL FAST instead of creating fake data
                raise ValueError(
                    f"CRITICAL: No 0DTE options found in data for 0DTE suite calculation. "
                    f"DTE range: {dte_values.min():.2f} to {dte_values.max():.2f}. "
                    "Cannot calculate 0DTE metrics without 0DTE data - NO FAKE DATA WILL BE SUBSTITUTED!"
                )

            # Calculate 0DTE-specific metrics using FAIL-FAST validation (NO FAKE DATA)
            vega_exposure = self._get_column_required(df_strike, 'total_vxoi_at_strike', 'for 0DTE suite calculation')
            gamma_exposure = self._get_column_required(df_strike, 'total_gxoi_at_strike', 'for 0DTE suite calculation')
            volume = self._get_column_required(df_strike, 'total_volume', 'for 0DTE suite calculation')
            vanna_exposure = self._get_column_required(df_strike, 'total_vannaxoi_at_strike', 'for 0DTE suite calculation')

            # VRI (Volatility Regime Indicator) for 0DTE - DISTINCT from VFI
            # VRI measures overall volatility environment strength
            vri_0dte = np.where(is_0dte, gamma_exposure * vega_exposure / np.maximum(volume, 1.0), 0.0)

            # VFI (Volatility Flow Intensity) for 0DTE - DISTINCT from VRI
            # VFI measures direction and momentum of volatility changes
            vfi_0dte = np.where(is_0dte, vega_exposure * 2.0, 0.0)  # 2x multiplier for 0DTE

            # VVR (Volatility-Volume Ratio) for 0DTE
            # VVR measures relationship between volatility and trading volume
            vvr_0dte = np.where(is_0dte, vega_exposure / np.maximum(volume, 1.0), 0.0)

            # VCI (Vanna Concentration Index) for 0DTE
            # VCI measures how different volatility measures are aligning
            total_vanna_0dte = vanna_exposure[is_0dte].sum()
            vci_0dte = np.where(is_0dte, vanna_exposure / max(abs(total_vanna_0dte), 1.0), 0.0)
            
            # Store results with correct field names for ProcessedStrikeLevelMetricsV2_5
            df_strike['vri_0dte_strike'] = vri_0dte  # VRI (Volatility Regime Indicator)
            df_strike['e_vfi_sens_strike'] = vfi_0dte  # VFI (Volatility Flow Intensity)
            df_strike['e_vvr_sens_strike'] = vvr_0dte  # VVR (Volatility-Volume Ratio)
            df_strike['vci_0dte_score'] = vci_0dte  # VCI (Vanna Concentration Index)
            
            return df_strike
            
        except Exception as e:
            self.logger.warning(f"Error calculating 0DTE suite: {e}")
            df_strike['vri_0dte_strike'] = 0.0  # VRI (Volatility Regime Indicator)
            df_strike['e_vfi_sens_strike'] = 0.0  # VFI (Volatility Flow Intensity)
            df_strike['e_vvr_sens_strike'] = 0.0  # VVR (Volatility-Volume Ratio)
            df_strike['vci_0dte_score'] = 0.0  # VCI (Vanna Concentration Index)
            return df_strike
    
    # =============================================================================
    # CONTEXT DETERMINATION - Optimized utilities
    # =============================================================================
    
    def _get_volatility_context_optimized(self, und_data) -> str:
        """Optimized volatility context determination with proper Pydantic access"""
        # FAIL-FAST: Require real volatility data - no fake defaults allowed
        current_iv = self._safe_float(getattr(und_data, 'u_volatility', None), 'u_volatility')
        
        if current_iv > self.VOLATILITY_THRESHOLDS['high']:
            return 'HIGH_VOL'
        elif current_iv < self.VOLATILITY_THRESHOLDS['low']:
            return 'LOW_VOL'
        else:
            return 'NORMAL_VOL'
    
    def _get_average_dte_context_optimized(self, df_strike: pd.DataFrame) -> str:
        """Optimized DTE context determination"""
        try:
            if 'dte' in df_strike.columns:
                avg_dte = df_strike['dte'].mean()
                
                if avg_dte <= self.DTE_THRESHOLDS['short']:
                    return 'SHORT_DTE'
                elif avg_dte <= self.DTE_THRESHOLDS['medium']:
                    return 'MEDIUM_DTE'
                else:
                    return 'LONG_DTE'
            else:
                return 'MEDIUM_DTE'  # Default
                
        except Exception:
            return 'MEDIUM_DTE'

    def _get_market_direction_bias_optimized(self, und_data) -> float:
        """Optimized market direction bias calculation"""
        try:
            # FAIL-FAST: Require real price change data - no fake defaults allowed
            price_change_pct = self._safe_float(getattr(und_data, 'price_change_pct_und', None), 'price_change_pct_und')

            # Simple directional bias calculation
            if abs(price_change_pct) > 0.005:  # 0.5% threshold
                return np.sign(price_change_pct) * min(abs(price_change_pct) * 20, 1.0)
            else:
                return 0.0

        except Exception as e:
            self.logger.error(f"CRITICAL: Error calculating market direction bias: {e}")
            raise ValueError(f"CRITICAL: Market direction bias calculation failed - cannot return fake 0.0 value! Error: {e}") from e

# Export the consolidated calculator
__all__ = ['AdaptiveCalculator', 'MarketRegime']
