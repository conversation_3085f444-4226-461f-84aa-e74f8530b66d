#!/usr/bin/env python3
"""
Dashboard Rendering Issue Investigation
Investigates why the dashboard is not rendering components properly.
"""

import sys
import logging
import requests
import time

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger("DashboardRenderingInvestigation")

def investigate_dashboard_response():
    """Investigate the actual dashboard response"""
    logger.info("🔍 INVESTIGATING DASHBOARD RESPONSE")
    
    try:
        response = requests.get("http://localhost:8050", timeout=10)
        
        logger.info(f"📊 Status Code: {response.status_code}")
        logger.info(f"📊 Content Length: {len(response.content)} bytes")
        logger.info(f"📊 Content Type: {response.headers.get('content-type', 'unknown')}")
        
        # Save the response to a file for analysis
        with open('dashboard_response_analysis.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        logger.info("📄 Dashboard response saved to 'dashboard_response_analysis.html'")
        
        # Analyze the content
        content = response.text
        
        # Check for Dash-specific content
        dash_indicators = [
            'dash-renderer',
            'dash-bootstrap-components',
            'ReactDOM.render',
            'window.dash_clientside',
            'DashRenderer'
        ]
        
        found_indicators = []
        for indicator in dash_indicators:
            if indicator in content:
                found_indicators.append(indicator)
        
        logger.info(f"📊 Dash indicators found: {found_indicators}")
        
        # Check for specific component IDs
        component_ids = [
            'main-data-store-id',
            'manual-refresh-button-id',
            'symbol-input-id',
            'page-content-id',
            'url-location-id'
        ]
        
        found_ids = []
        for comp_id in component_ids:
            if comp_id in content:
                found_ids.append(comp_id)
        
        logger.info(f"📊 Component IDs found: {found_ids}")
        
        # Check for error messages
        error_indicators = [
            'error',
            'Error',
            'ERROR',
            'exception',
            'Exception',
            'failed',
            'Failed'
        ]
        
        found_errors = []
        for error in error_indicators:
            if error in content.lower():
                found_errors.append(error)
        
        if found_errors:
            logger.warning(f"⚠️ Potential errors in response: {found_errors}")
        
        # Check if it's a basic HTML page or a Dash app
        if '<html' in content and 'dash' not in content.lower():
            logger.error("❌ CRITICAL: Dashboard appears to be serving basic HTML, not a Dash app")
            return False
        elif len(found_indicators) == 0:
            logger.error("❌ CRITICAL: No Dash indicators found in response")
            return False
        else:
            logger.info("✅ Dashboard appears to be a Dash application")
            return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Dashboard response investigation error - {e}")
        return False

def check_dashboard_initialization():
    """Check if dashboard initialization is complete"""
    logger.info("🔍 CHECKING DASHBOARD INITIALIZATION")
    
    try:
        # Check if the dashboard is still initializing
        response = requests.get("http://localhost:8050", timeout=5)
        content = response.text
        
        # Look for initialization indicators
        init_indicators = [
            'Waiting for initial data fetch',
            'Loading',
            'Initializing',
            'Please wait'
        ]
        
        found_init = []
        for indicator in init_indicators:
            if indicator in content:
                found_init.append(indicator)
        
        if found_init:
            logger.info(f"📊 Dashboard initialization messages: {found_init}")
            logger.info("⏳ Dashboard may still be initializing - this could explain missing components")
            return "initializing"
        else:
            logger.info("📊 No initialization messages found")
            return "ready"
        
    except Exception as e:
        logger.error(f"❌ FAILED: Dashboard initialization check error - {e}")
        return "error"

def test_dashboard_endpoints():
    """Test various dashboard endpoints"""
    logger.info("🔍 TESTING DASHBOARD ENDPOINTS")
    
    endpoints = [
        "/",
        "/_dash-layout",
        "/_dash-dependencies",
        "/_dash-update-component"
    ]
    
    for endpoint in endpoints:
        try:
            url = f"http://localhost:8050{endpoint}"
            response = requests.get(url, timeout=5)
            logger.info(f"📊 {endpoint}: Status {response.status_code}, Size {len(response.content)} bytes")
            
            if endpoint == "/_dash-layout":
                # This should contain the layout JSON
                if response.status_code == 200:
                    logger.info("✅ Dash layout endpoint is accessible")
                    # Save layout for analysis
                    with open('dashboard_layout_analysis.json', 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    logger.info("📄 Dashboard layout saved to 'dashboard_layout_analysis.json'")
                else:
                    logger.error("❌ Dash layout endpoint is not accessible")
            
        except Exception as e:
            logger.error(f"❌ Endpoint {endpoint} error: {e}")

def analyze_component_rendering():
    """Analyze why components are not rendering"""
    logger.info("🔍 ANALYZING COMPONENT RENDERING")
    
    try:
        # Check if the layout creation is working
        from dashboard_application.layout_manager_v2_5 import create_master_layout
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        config_manager = ConfigManagerV2_5()
        layout = create_master_layout(config_manager)
        
        # Convert layout to JSON to see its structure
        layout_json = layout.to_plotly_json()
        
        # Save layout JSON for analysis
        import json
        with open('layout_structure_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(layout_json, f, indent=2)
        
        logger.info("📄 Layout structure saved to 'layout_structure_analysis.json'")
        
        # Check if layout contains expected components
        def find_components_recursive(obj, components=None):
            if components is None:
                components = []
            
            if isinstance(obj, dict):
                if 'props' in obj and 'id' in obj['props']:
                    components.append(obj['props']['id'])
                for value in obj.values():
                    find_components_recursive(value, components)
            elif isinstance(obj, list):
                for item in obj:
                    find_components_recursive(item, components)
            
            return components
        
        components = find_components_recursive(layout_json)
        logger.info(f"📊 Components in layout: {len(components)}")
        
        # Check for critical components
        critical_components = [
            'main-data-store-id',
            'manual-refresh-button-id',
            'symbol-input-id',
            'page-content-id'
        ]
        
        found_critical = []
        for comp in critical_components:
            if comp in components:
                found_critical.append(comp)
        
        logger.info(f"📊 Critical components found: {found_critical}")
        
        if len(found_critical) == len(critical_components):
            logger.info("✅ All critical components are in the layout")
            logger.info("🔍 Issue may be with Dash rendering, not layout creation")
        else:
            logger.error(f"❌ Missing critical components: {set(critical_components) - set(found_critical)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Component rendering analysis error - {e}")
        return False

def main():
    """Run dashboard rendering investigation"""
    logger.info("🎯 STARTING DASHBOARD RENDERING INVESTIGATION")
    logger.info("=" * 80)
    
    # Step 1: Investigate dashboard response
    logger.info("STEP 1: Dashboard Response Analysis")
    response_ok = investigate_dashboard_response()
    logger.info("-" * 40)
    
    # Step 2: Check initialization status
    logger.info("STEP 2: Dashboard Initialization Check")
    init_status = check_dashboard_initialization()
    logger.info("-" * 40)
    
    # Step 3: Test dashboard endpoints
    logger.info("STEP 3: Dashboard Endpoints Test")
    test_dashboard_endpoints()
    logger.info("-" * 40)
    
    # Step 4: Analyze component rendering
    logger.info("STEP 4: Component Rendering Analysis")
    rendering_ok = analyze_component_rendering()
    logger.info("-" * 40)
    
    # Summary
    logger.info("=" * 80)
    logger.info("🎯 DASHBOARD RENDERING INVESTIGATION SUMMARY")
    
    if response_ok and rendering_ok:
        logger.info("✅ Dashboard structure appears correct")
        if init_status == "initializing":
            logger.info("⏳ Dashboard may still be initializing - wait for data fetch")
        else:
            logger.info("🔍 Issue may be with client-side rendering or JavaScript")
    else:
        logger.error("❌ Critical issues found with dashboard structure")
    
    logger.info("\n🔧 RECOMMENDED ACTIONS:")
    logger.info("1. Check 'dashboard_response_analysis.html' for actual HTML content")
    logger.info("2. Check 'dashboard_layout_analysis.json' for Dash layout structure")
    logger.info("3. Check 'layout_structure_analysis.json' for component hierarchy")
    logger.info("4. Monitor dashboard terminal for initialization completion")
    logger.info("5. Check browser developer console for JavaScript errors")
    
    return response_ok and rendering_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
