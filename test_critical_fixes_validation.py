#!/usr/bin/env python3
"""
CRITICAL FIXES VALIDATION
=========================

Comprehensive test to validate all the critical fixes for:
1. Tradier historical data fetcher None handling
2. Flow analytics cache insufficient data handling
3. Elite intelligence missing historical data handling
4. Implied volatility field mapping and validation

This test ensures the system fails fast appropriately and doesn't
produce fake data during any scenario.
"""

import asyncio
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_tradier_none_handling():
    """Test that Tradier data fetcher handles None responses properly"""
    logger.info("🧪 TESTING: Tradier None Handling Fix")
    
    try:
        from data_management.tradier_data_fetcher_v2_5 import TradierDataFetcherV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        config = ConfigManagerV2_5()
        fetcher = TradierDataFetcherV2_5(config)
        
        # This should not crash with TypeError anymore
        try:
            result = await fetcher.fetch_historical_data('SPX', days=5)
            logger.info(f"✅ Tradier fetch completed without TypeError: {type(result)}")
            return True
        except TypeError as e:
            if "argument of type 'NoneType' is not iterable" in str(e):
                logger.error("❌ Tradier None handling fix FAILED - TypeError still occurring")
                return False
            else:
                # Other TypeErrors are acceptable
                logger.info(f"✅ Different TypeError (acceptable): {e}")
                return True
        except Exception as e:
            # Other exceptions are acceptable (API errors, etc.)
            logger.info(f"✅ Non-TypeError exception (acceptable): {type(e).__name__}: {e}")
            return True
            
    except Exception as e:
        logger.error(f"❌ Tradier test setup error: {e}", exc_info=True)
        return False

async def test_flow_analytics_cache_handling():
    """Test that flow analytics handles insufficient cache data properly"""
    logger.info("🧪 TESTING: Flow Analytics Cache Handling")
    
    try:
        from core_analytics_engine.eots_metrics.flow_analytics import FlowAnalytics
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        config = ConfigManagerV2_5()
        flow_analytics = FlowAnalytics(config)
        
        # Test z-score calculation with insufficient data
        try:
            # This should raise a clear error, not return 0.0
            result = flow_analytics._calculate_z_score_optimized([], 1.0)
            logger.error(f"❌ Flow analytics should have failed but returned: {result}")
            return False
        except ValueError as e:
            if "Insufficient cache data for Z-score calculation" in str(e):
                logger.info("✅ Flow analytics properly fails fast with insufficient cache data")
                return True
            else:
                logger.error(f"❌ Unexpected ValueError: {e}")
                return False
        except Exception as e:
            logger.error(f"❌ Unexpected exception type: {type(e).__name__}: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Flow analytics test setup error: {e}", exc_info=True)
        return False

async def test_elite_intelligence_historical_data():
    """Test that elite intelligence handles missing historical data properly"""
    logger.info("🧪 TESTING: Elite Intelligence Historical Data Handling")
    
    try:
        from core_analytics_engine.eots_metrics.elite_intelligence import EliteImpactCalculator
        from data_models import ProcessedUnderlyingAggregatesV2_5
        from datetime import datetime
        
        calculator = EliteImpactCalculator()
        
        # Create test data with missing price_change_pct_und (None)
        test_underlying_data = ProcessedUnderlyingAggregatesV2_5(
            symbol='SPX',
            timestamp=datetime.now(),
            price=6173.07,
            price_change_abs_und=None,  # Missing historical data
            price_change_pct_und=None,  # Missing historical data
            day_open_price_und=None,
            day_high_price_und=None,
            day_low_price_und=None,
            prev_day_close_price_und=None,
            u_volatility=0.1632,
            day_volume=0.0,
            call_gxoi=4827.246622748356,
            put_gxoi=2652.1893672437327,
            gammas_call_buy=72.10227623820384,
            gammas_call_sell=80.44132793864988,
            gammas_put_buy=53.581534789894405,
            gammas_put_sell=47.26153479,
            deltas_call_buy=0.0,
            deltas_call_sell=0.0,
            deltas_put_buy=0.0,
            deltas_put_sell=0.0,
            total_dxoi_und=158226.916253865,
            total_gxoi_und=407.8032030877779,
            total_vxoi_und=8927050.064595502,
            total_txoi_und=-150007260.62502784,
            total_charmxoi_und=0.0,
            total_vannaxoi_und=-256207.8608443079,
            total_vommaxoi_und=0.0,
            nvp_und=0.0,
            nvp_vol_und=0.0,
            total_nvp_und=0.0,
            total_nvp_vol_und=0.0,
            a_dag_und_aggregate=407.8032030877779,
            e_sdag_und_aggregate=407.8032030877779
        )
        
        try:
            # This should fail fast, not return fake data
            result = calculator._calculate_momentum_persistence_optimized(test_underlying_data)
            logger.error(f"❌ Elite intelligence should have failed but returned: {result}")
            return False
        except ValueError as e:
            if ("Historical price data not available" in str(e) or 
                "price_change_pct_und is None during market hours" in str(e)):
                logger.info("✅ Elite intelligence properly fails fast with missing historical data")
                return True
            else:
                logger.error(f"❌ Unexpected ValueError: {e}")
                return False
        except Exception as e:
            logger.error(f"❌ Unexpected exception type: {type(e).__name__}: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Elite intelligence test setup error: {e}", exc_info=True)
        return False

async def test_implied_volatility_validation():
    """Test that implied volatility field validation works properly"""
    logger.info("🧪 TESTING: Implied Volatility Field Validation")
    
    try:
        from data_management.initial_processor_v2_5 import prepare_data_for_processing
        from data_models import ProcessedUnderlyingAggregatesV2_5
        import pandas as pd
        from datetime import datetime
        
        # Create test data WITHOUT iv field
        test_df = pd.DataFrame({
            'strike': [6100, 6150, 6200],
            'symbol': ['SPX', 'SPX', 'SPX'],
            'dte_calc': [1, 1, 1],
            'volume': [100, 200, 150],
            'open_interest': [500, 600, 550]
            # Note: NO 'iv' field - this should cause failure
        })
        
        test_underlying_data = ProcessedUnderlyingAggregatesV2_5(
            symbol='SPX',
            timestamp=datetime.now(),
            price=6173.07,
            price_change_abs_und=None,
            price_change_pct_und=None,
            day_open_price_und=None,
            day_high_price_und=None,
            day_low_price_und=None,
            prev_day_close_price_und=None,
            u_volatility=0.1632,
            day_volume=0.0,
            call_gxoi=4827.246622748356,
            put_gxoi=2652.1893672437327,
            gammas_call_buy=72.10227623820384,
            gammas_call_sell=80.44132793864988,
            gammas_put_buy=53.581534789894405,
            gammas_put_sell=47.26153479,
            deltas_call_buy=0.0,
            deltas_call_sell=0.0,
            deltas_put_buy=0.0,
            deltas_put_sell=0.0,
            total_dxoi_und=158226.916253865,
            total_gxoi_und=407.8032030877779,
            total_vxoi_und=8927050.064595502,
            total_txoi_und=-150007260.62502784,
            total_charmxoi_und=0.0,
            total_vannaxoi_und=-256207.8608443079,
            total_vommaxoi_und=0.0,
            nvp_und=0.0,
            nvp_vol_und=0.0,
            total_nvp_und=0.0,
            total_nvp_vol_und=0.0,
            a_dag_und_aggregate=407.8032030877779,
            e_sdag_und_aggregate=407.8032030877779
        )
        
        try:
            # This should fail fast due to missing IV data
            result_df, result_underlying = prepare_data_for_processing(test_df, test_underlying_data, 'SPX')
            logger.error("❌ IV validation should have failed but succeeded")
            return False
        except ValueError as e:
            if "No implied volatility data available" in str(e):
                logger.info("✅ IV validation properly fails fast with missing IV data")
                return True
            else:
                logger.error(f"❌ Unexpected ValueError: {e}")
                return False
        except Exception as e:
            logger.error(f"❌ Unexpected exception type: {type(e).__name__}: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ IV validation test setup error: {e}", exc_info=True)
        return False

async def main():
    """Run all critical fixes validation tests"""
    logger.info("🚀 CRITICAL FIXES VALIDATION SUITE")
    logger.info("=" * 60)
    
    # Test 1: Tradier None handling
    tradier_success = await test_tradier_none_handling()
    
    logger.info("=" * 60)
    
    # Test 2: Flow analytics cache handling
    flow_success = await test_flow_analytics_cache_handling()
    
    logger.info("=" * 60)
    
    # Test 3: Elite intelligence historical data
    elite_success = await test_elite_intelligence_historical_data()
    
    logger.info("=" * 60)
    
    # Test 4: Implied volatility validation
    iv_success = await test_implied_volatility_validation()
    
    # Summary
    logger.info("=" * 60)
    logger.info("📊 CRITICAL FIXES VALIDATION RESULTS:")
    logger.info(f"✅ Tradier None Handling: {'PASS' if tradier_success else 'FAIL'}")
    logger.info(f"✅ Flow Analytics Cache: {'PASS' if flow_success else 'FAIL'}")
    logger.info(f"✅ Elite Intelligence Historical: {'PASS' if elite_success else 'FAIL'}")
    logger.info(f"✅ Implied Volatility Validation: {'PASS' if iv_success else 'FAIL'}")
    
    all_passed = tradier_success and flow_success and elite_success and iv_success
    
    if all_passed:
        logger.info("🎉 ALL CRITICAL FIXES VALIDATED - System properly fails fast!")
        logger.info("📋 Key improvements:")
        logger.info("   • No more TypeError from Tradier None responses")
        logger.info("   • Flow analytics fails fast instead of returning 0.0")
        logger.info("   • Elite intelligence fails fast with missing historical data")
        logger.info("   • IV validation prevents fake volatility data")
        logger.info("   • Zero tolerance fake data policy enforced")
    else:
        logger.error("💥 SOME CRITICAL FIXES FAILED - Additional work needed")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
