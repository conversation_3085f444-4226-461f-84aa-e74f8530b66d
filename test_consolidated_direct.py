# test_consolidated_direct.py

"""
Direct test for consolidated EOTS metrics modules.
Tests modules directly without importing the full package to avoid Pydantic conflicts.
"""

import sys
import os
import traceback
from unittest.mock import Mock
import pandas as pd
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

def create_mock_managers():
    """Create mock managers for testing"""
    config_manager = Mock()
    historical_data_manager = Mock()
    enhanced_cache_manager = Mock()
    enhanced_cache_manager.get.return_value = None
    enhanced_cache_manager.set.return_value = None
    return config_manager, historical_data_manager, enhanced_cache_manager

def test_core_calculator_direct():
    """Test CoreCalculator directly"""
    print("🧪 Testing CoreCalculator (Direct Import)...")
    
    try:
        # Direct import to avoid package-level conflicts
        sys.path.insert(0, 'core_analytics_engine/eots_metrics')
        from core_calculator import CoreCalculator
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Initialize calculator
        calculator = CoreCalculator(config_manager, historical_data_manager, enhanced_cache_manager)
        
        # Test utility functions
        assert calculator._safe_float(10) == 10.0
        assert calculator._safe_float("15.5") == 15.5
        assert calculator._safe_float(None) == 0.0
        assert calculator._bound_value(5, 0, 10) == 5
        assert calculator._bound_value(-5, 0, 10) == 0
        
        # Test sample data
        sample_data = {
            'symbol': 'SPY',
            'price': 450.0,
            'deltas_buy': 1000.0,
            'deltas_sell': 800.0,
            'gammas_call_buy': 500.0,
            'gammas_call_sell': 300.0,
            'gammas_put_buy': 400.0,
            'gammas_put_sell': 200.0,
            'vegas_buy': 2000.0,
            'vegas_sell': 1500.0,
            'thetas_buy': 800.0,
            'thetas_sell': 600.0,
            'call_gxoi': 15000.0,
            'put_gxoi': 18000.0
        }
        
        # Test foundational metrics calculation
        result = calculator.calculate_all_foundational_metrics(sample_data)
        
        # Verify results
        assert 'net_cust_delta_flow_und' in result
        assert 'net_cust_gamma_flow_und' in result
        assert 'gib_oi_based_und' in result
        assert 'hp_eod_und' in result
        
        # Verify calculations
        assert result['net_cust_delta_flow_und'] == 200.0  # 1000 - 800
        assert result['net_cust_gamma_flow_und'] == 400.0  # (500+400) - (300+200)
        
        print("✅ CoreCalculator direct test passed!")
        return True
        
    except Exception as e:
        print(f"❌ CoreCalculator direct test failed: {e}")
        traceback.print_exc()
        return False

def test_flow_analytics_direct():
    """Test FlowAnalytics directly"""
    print("🧪 Testing FlowAnalytics (Direct Import)...")
    
    try:
        # Direct import
        sys.path.insert(0, 'core_analytics_engine/eots_metrics')
        from flow_analytics import FlowAnalytics, FlowType
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Initialize calculator
        calculator = FlowAnalytics(config_manager, historical_data_manager, enhanced_cache_manager)
        
        # Test sample data
        sample_data = {
            'symbol': 'SPY',
            'net_value_flow_5m_und': 50000.0,
            'net_vol_flow_5m_und': 10000.0,
            'net_vol_flow_15m_und': 25000.0,
            'net_vol_flow_30m_und': 40000.0,
            'u_volatility': 0.20,
            'day_volume': 30000000
        }
        
        # Test flow type classification
        flow_type = calculator._classify_flow_type_optimized(sample_data)
        assert isinstance(flow_type, FlowType)
        
        # Test momentum calculation
        momentum = calculator._calculate_momentum_acceleration_index_optimized(sample_data)
        assert isinstance(momentum, (int, float))
        assert -10.0 <= momentum <= 10.0  # Should be bounded
        
        print("✅ FlowAnalytics direct test passed!")
        return True
        
    except Exception as e:
        print(f"❌ FlowAnalytics direct test failed: {e}")
        traceback.print_exc()
        return False

def test_elite_intelligence_direct():
    """Test EliteIntelligence directly"""
    print("🧪 Testing EliteIntelligence (Direct Import)...")
    
    try:
        # Direct import
        sys.path.insert(0, 'core_analytics_engine/eots_metrics')
        from elite_intelligence import EliteImpactCalculator, EliteConfig
        
        # Initialize calculator
        elite_config = EliteConfig()
        calculator = EliteImpactCalculator(elite_config)
        
        # Test sample data
        sample_options_data = pd.DataFrame({
            'strike': [440, 445, 450, 455, 460],
            'volume': [1000, 1500, 2000, 1500, 1000]
        })
        
        sample_underlying_data = {
            'net_value_flow_5m_und': 50000.0,
            'net_vol_flow_5m_und': 10000.0,
            'day_volume': 30000000,
            'u_volatility': 0.25,
            'price_change_pct_und': 0.015
        }
        
        # Test elite impact calculation
        result = calculator.calculate_elite_impact_score(sample_options_data, sample_underlying_data)
        assert isinstance(result, dict)
        assert 'elite_impact_score_und' in result
        assert 'institutional_flow_score_und' in result
        assert 'market_regime_elite' in result
        
        # Verify score bounds
        assert 0.0 <= result['elite_impact_score_und'] <= 100.0
        assert 0.0 <= result['institutional_flow_score_und'] <= 100.0
        
        print("✅ EliteIntelligence direct test passed!")
        return True
        
    except Exception as e:
        print(f"❌ EliteIntelligence direct test failed: {e}")
        traceback.print_exc()
        return False

def test_supplementary_metrics_direct():
    """Test SupplementaryMetrics directly"""
    print("🧪 Testing SupplementaryMetrics (Direct Import)...")
    
    try:
        # Direct import
        sys.path.insert(0, 'core_analytics_engine/eots_metrics')
        from supplementary_metrics import SupplementaryMetrics, AdvancedOptionsMetrics
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Mock historical data manager for ATR calculation
        historical_data_manager.get_historical_ohlcv.return_value = pd.DataFrame({
            'high': [451, 452, 453, 454, 455],
            'low': [449, 450, 451, 452, 453],
            'close': [450, 451, 452, 453, 454]
        })
        
        # Initialize calculator
        calculator = SupplementaryMetrics(config_manager, historical_data_manager, enhanced_cache_manager)
        
        # Test ATR calculation
        atr = calculator.calculate_atr('SPY', dte_max=30)
        assert isinstance(atr, (int, float))
        assert atr >= 0.0  # ATR should be non-negative
        
        # Test advanced options metrics
        sample_options_data = pd.DataFrame({
            'strike': [440, 445, 450, 455, 460],
            'volume': [1000, 1500, 2000, 1500, 1000]
        })
        
        sample_underlying_data = {
            'day_volume': 30000000,
            'net_value_flow_5m_und': 50000.0,
            'net_vol_flow_5m_und': 10000.0,
            'u_volatility': 0.25
        }
        
        advanced_metrics = calculator.calculate_advanced_options_metrics(sample_options_data, sample_underlying_data)
        assert isinstance(advanced_metrics, AdvancedOptionsMetrics)
        
        # Test metrics dictionary conversion
        metrics_dict = advanced_metrics.to_dict()
        assert 'lwpai' in metrics_dict
        assert 'vabai' in metrics_dict
        assert 'aofm' in metrics_dict
        assert 'lidb' in metrics_dict
        
        print("✅ SupplementaryMetrics direct test passed!")
        return True
        
    except Exception as e:
        print(f"❌ SupplementaryMetrics direct test failed: {e}")
        traceback.print_exc()
        return False

def test_integration_workflow():
    """Test a basic integration workflow"""
    print("🧪 Testing Integration Workflow...")
    
    try:
        # Import all modules directly
        sys.path.insert(0, 'core_analytics_engine/eots_metrics')
        from core_calculator import CoreCalculator
        from flow_analytics import FlowAnalytics
        from elite_intelligence import EliteImpactCalculator, EliteConfig
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Initialize calculators
        core_calc = CoreCalculator(config_manager, historical_data_manager, enhanced_cache_manager)
        flow_calc = FlowAnalytics(config_manager, historical_data_manager, enhanced_cache_manager)
        elite_calc = EliteImpactCalculator(EliteConfig())
        
        # Sample data
        underlying_data = {
            'symbol': 'SPY',
            'price': 450.0,
            'deltas_buy': 1000.0,
            'deltas_sell': 800.0,
            'gammas_call_buy': 500.0,
            'gammas_call_sell': 300.0,
            'gammas_put_buy': 400.0,
            'gammas_put_sell': 200.0,
            'vegas_buy': 2000.0,
            'vegas_sell': 1500.0,
            'thetas_buy': 800.0,
            'thetas_sell': 600.0,
            'call_gxoi': 15000.0,
            'put_gxoi': 18000.0,
            'net_value_flow_5m_und': 50000.0,
            'net_vol_flow_5m_und': 10000.0,
            'net_vol_flow_15m_und': 25000.0,
            'net_vol_flow_30m_und': 40000.0,
            'u_volatility': 0.20,
            'day_volume': 30000000
        }
        
        options_data = pd.DataFrame({
            'strike': [440, 445, 450, 455, 460],
            'volume': [1000, 1500, 2000, 1500, 1000]
        })
        
        # Step 1: Calculate foundational metrics
        result = core_calc.calculate_all_foundational_metrics(underlying_data.copy())
        assert 'net_cust_delta_flow_und' in result
        
        # Step 2: Calculate flow metrics
        result = flow_calc.calculate_all_enhanced_flow_metrics(result, 'SPY')
        assert 'flow_type_elite' in result
        
        # Step 3: Calculate elite impact
        elite_result = elite_calc.calculate_elite_impact_score(options_data, result)
        assert 'elite_impact_score_und' in elite_result
        
        # Combine results
        final_result = {**result, **elite_result}
        
        # Verify comprehensive result
        expected_keys = [
            'net_cust_delta_flow_und', 'gib_oi_based_und', 'hp_eod_und',
            'flow_type_elite', 'momentum_acceleration_index_und',
            'elite_impact_score_und', 'institutional_flow_score_und'
        ]
        
        for key in expected_keys:
            assert key in final_result, f"Missing key: {key}"
        
        print("✅ Integration Workflow test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Integration Workflow test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all direct tests"""
    print("🚀 Starting Consolidated Metrics Direct Tests\n")
    
    tests = [
        test_core_calculator_direct,
        test_flow_analytics_direct,
        test_elite_intelligence_direct,
        test_supplementary_metrics_direct,
        test_integration_workflow
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All direct tests passed! Consolidated modules are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
