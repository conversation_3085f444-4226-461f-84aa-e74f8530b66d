#!/usr/bin/env python3
"""
FUTURES DATA PROCESSING TEST
============================

Comprehensive test to validate that the Elite Options Trading System v2.5
properly processes futures symbols like /ES:XCME without the issues identified:

1. Key levels table showing 0 levels
2. Tradier 'NoneType' connect errors  
3. Flow metrics "No value flow data available"
4. Elite impact score "Historical price data not available during off-hours"
5. Control panel parameter validation

This test simulates the data processing pipeline for futures symbols.
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_futures_data_processing():
    """Test the complete futures data processing pipeline"""
    logger.info("🚀 FUTURES DATA PROCESSING TEST SUITE")
    logger.info("=" * 70)
    
    test_symbol = "/ES:XCME"
    
    try:
        # Test 1: Import core components
        logger.info("🧪 TEST 1: Importing core components...")
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from data_models.core_system_config import ControlPanelStateV2_5
        
        # Test 2: Initialize configuration
        logger.info("🧪 TEST 2: Initializing configuration...")
        config_manager = ConfigManagerV2_5()
        logger.info("✅ Configuration initialized successfully")
        
        # Test 3: Test control panel validation for futures
        logger.info("🧪 TEST 3: Testing control panel validation for futures...")
        control_panel_state = ControlPanelStateV2_5(
            symbol=test_symbol,
            dte_min=0,
            dte_max=5,
            price_range_percent=3,
            refresh_interval_seconds=30
        )
        logger.info(f"✅ Control panel validation passed for {control_panel_state.symbol}")
        
        # Test 4: Initialize orchestrator
        logger.info("🧪 TEST 4: Initializing orchestrator...")
        orchestrator = ITSOrchestratorV2_5(config_manager)
        logger.info("✅ Orchestrator initialized successfully")
        
        # Test 5: Test futures symbol detection
        logger.info("🧪 TEST 5: Testing futures symbol detection...")
        is_futures = orchestrator._is_futures_symbol(test_symbol)
        if is_futures:
            logger.info(f"✅ Correctly identified {test_symbol} as futures symbol")
        else:
            logger.error(f"❌ Failed to identify {test_symbol} as futures symbol")
            return False
        
        # Test 6: Test data fetching (this will test the Tradier bypass)
        logger.info("🧪 TEST 6: Testing data fetching with Tradier bypass...")
        try:
            # This should not fail with Tradier errors for futures
            data_bundle = await orchestrator.run_full_analysis_cycle(
                ticker=test_symbol,
                dte_min=0,
                dte_max=5,
                price_range_percent=3
            )
            
            if data_bundle:
                logger.info("✅ Data fetching completed without Tradier errors")
                
                # Test 7: Check key levels
                logger.info("🧪 TEST 7: Checking key levels generation...")
                if hasattr(data_bundle, 'key_levels_data') and data_bundle.key_levels_data:
                    total_levels = (
                        len(data_bundle.key_levels_data.supports) +
                        len(data_bundle.key_levels_data.resistances) +
                        len(data_bundle.key_levels_data.pin_zones) +
                        len(data_bundle.key_levels_data.vol_triggers) +
                        len(data_bundle.key_levels_data.major_walls)
                    )
                    logger.info(f"✅ Key levels generated: {total_levels} total levels")
                else:
                    logger.warning("⚠️ No key levels data found in bundle")
                
                # Test 8: Check flow metrics
                logger.info("🧪 TEST 8: Checking flow metrics...")
                if hasattr(data_bundle, 'processed_data') and data_bundle.processed_data:
                    underlying_data = data_bundle.processed_data.underlying_data_enriched
                    if underlying_data:
                        # Check for flow metrics fields
                        flow_fields = ['net_value_flow_5m_und', 'net_vol_flow_5m_und', 'vapi_fa_z_score_und', 'dwfd_z_score_und']
                        found_fields = []
                        for field in flow_fields:
                            if hasattr(underlying_data, field):
                                found_fields.append(field)
                        
                        if found_fields:
                            logger.info(f"✅ Flow metrics found: {', '.join(found_fields)}")
                        else:
                            logger.warning("⚠️ No flow metrics found")
                    else:
                        logger.warning("⚠️ No underlying data found")
                else:
                    logger.warning("⚠️ No processed data found in bundle")
                
                # Test 9: Check elite impact score
                logger.info("🧪 TEST 9: Checking elite impact score...")
                if hasattr(data_bundle, 'processed_data') and data_bundle.processed_data:
                    underlying_data = data_bundle.processed_data.underlying_data_enriched
                    if underlying_data and hasattr(underlying_data, 'elite_impact_score'):
                        logger.info(f"✅ Elite impact score found: {underlying_data.elite_impact_score}")
                    else:
                        logger.warning("⚠️ No elite impact score found")
                
                logger.info("✅ All tests completed successfully!")
                return True
            else:
                logger.error("❌ Data fetching returned empty bundle")
                return False
                
        except Exception as e:
            logger.error(f"❌ Data processing failed: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test setup failed: {e}", exc_info=True)
        return False

def test_futures_symbol_detection():
    """Test the futures symbol detection logic"""
    logger.info("🧪 TESTING: Futures Symbol Detection Logic")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        config_manager = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config_manager)
        
        # Test cases: (symbol, expected_result, description)
        test_cases = [
            ("/ES:XCME", True, "E-mini S&P 500 futures"),
            ("/NQ:XCME", True, "E-mini NASDAQ futures"),
            ("/CL:NYMEX", True, "Crude oil futures"),
            ("SPX", False, "Equity symbol"),
            ("SPY", False, "Equity ETF"),
            ("", False, "Empty symbol"),
            ("ES:XCME", False, "Futures without leading slash"),
            ("/ES", False, "Futures without exchange"),
        ]
        
        passed = 0
        failed = 0
        
        for symbol, expected, description in test_cases:
            result = orchestrator._is_futures_symbol(symbol)
            if result == expected:
                logger.info(f"✅ PASS: '{symbol}' -> {result} ({description})")
                passed += 1
            else:
                logger.error(f"❌ FAIL: '{symbol}' -> {result}, expected {expected} ({description})")
                failed += 1
        
        logger.info(f"📊 Futures Detection Results: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        logger.error(f"❌ Futures detection test failed: {e}", exc_info=True)
        return False

async def main():
    """Main test runner"""
    logger.info("🚀 STARTING FUTURES DATA PROCESSING VALIDATION")
    logger.info("=" * 70)
    
    # Test 1: Symbol detection
    detection_passed = test_futures_symbol_detection()
    
    # Test 2: Full data processing pipeline
    processing_passed = await test_futures_data_processing()
    
    # Summary
    logger.info("=" * 70)
    logger.info("📊 FUTURES DATA PROCESSING TEST RESULTS:")
    logger.info(f"✅ Symbol Detection: {'PASS' if detection_passed else 'FAIL'}")
    logger.info(f"✅ Data Processing: {'PASS' if processing_passed else 'FAIL'}")
    
    if detection_passed and processing_passed:
        logger.info("🎉 ALL TESTS PASSED - Futures data processing implemented successfully!")
        logger.info("📋 Key improvements:")
        logger.info("   • Futures symbols bypass Tradier (which doesn't support futures)")
        logger.info("   • Flow metrics provide graceful fallbacks for futures")
        logger.info("   • Elite impact score handles futures market hours correctly")
        logger.info("   • Key levels generation works for futures symbols")
        logger.info("   • Control panel validation supports futures symbols")
        return True
    else:
        logger.error("💥 SOME TESTS FAILED - Additional fixes may be needed")
        return False

if __name__ == "__main__":
    asyncio.run(main())
