#!/usr/bin/env python3
"""
Systematic fix for all deprecated data_models imports.
This script will update ALL files to use the new consolidated data_models structure.
"""

import os
import re
import sys
from pathlib import Path

# Mapping of deprecated imports to the new consolidated structure
DEPRECATED_MODULES = {
    'data_models.processed_data',
    'data_models.bundle_schemas', 
    'data_models.signal_level_schemas',
    'data_models.context_schemas',
    'data_models.configuration_schemas',
    'data_models.system_schemas',
    'data_models.atif_schemas',
    'data_models.recommendation_schemas',
    'data_models.learning_schemas',
    'data_models.hui_hui_schemas',
    'data_models.moe_schemas_v2_5',
    'data_models.ai_predictions',
    'data_models.processed_data_config_schemas'
}

def fix_imports_in_file(file_path):
    """Fix deprecated imports in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # Pattern to match deprecated imports
        for deprecated_module in DEPRECATED_MODULES:
            # Pattern 1: from data_models.deprecated_module import ...
            pattern1 = rf'from {re.escape(deprecated_module)} import'
            replacement1 = 'from data_models import'
            
            if re.search(pattern1, content):
                content = re.sub(pattern1, replacement1, content)
                changes_made.append(f"  - Fixed: {deprecated_module} -> data_models")
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Fixed {file_path}")
            for change in changes_made:
                print(change)
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def find_and_fix_all_files():
    """Find and fix all Python files with deprecated imports."""
    print("🔍 Finding all Python files with deprecated data_models imports...")
    
    # Find all Python files (excluding deprecated folders)
    python_files = []
    for root, dirs, files in os.walk('.'):
        # Skip deprecated directories
        dirs[:] = [d for d in dirs if 'deprecated' not in d.lower() and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                python_files.append(file_path)
    
    print(f"📁 Found {len(python_files)} Python files to check")
    
    # Check each file for deprecated imports
    files_with_deprecated_imports = []
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if file has any deprecated imports
            has_deprecated = False
            for deprecated_module in DEPRECATED_MODULES:
                if f'from {deprecated_module} import' in content:
                    has_deprecated = True
                    break
            
            if has_deprecated:
                files_with_deprecated_imports.append(file_path)
                
        except Exception as e:
            print(f"⚠️  Could not read {file_path}: {e}")
    
    print(f"🎯 Found {len(files_with_deprecated_imports)} files with deprecated imports")
    
    # Fix each file
    fixed_count = 0
    for file_path in files_with_deprecated_imports:
        if fix_imports_in_file(file_path):
            fixed_count += 1
    
    print(f"\n📊 RESULTS:")
    print(f"✅ Fixed: {fixed_count} files")
    print(f"📁 Total checked: {len(python_files)} files")
    print(f"🎯 Files with deprecated imports: {len(files_with_deprecated_imports)}")
    
    return fixed_count > 0

def main():
    """Main function to run the systematic fix."""
    print("🚀 SYSTEMATIC IMPORT FIX")
    print("=" * 60)
    print("Fixing all deprecated data_models imports to use consolidated structure")
    print("=" * 60)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Run the fix
    success = find_and_fix_all_files()
    
    if success:
        print("\n🎉 SYSTEMATIC FIX COMPLETED!")
        print("All deprecated imports have been updated to use the consolidated data_models structure.")
        print("\nNext steps:")
        print("1. Test the system to ensure all imports work")
        print("2. Run your test suite to verify functionality")
        print("3. Remove any remaining deprecated files if needed")
    else:
        print("\n⚠️  No deprecated imports found or no changes made.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
