#!/usr/bin/env python3
"""
Root cause analysis test to identify why required fields are None during live market hours.
This test will trace the complete data pipeline to find where the data is getting lost.
"""

import sys
import os
sys.path.append('.')

from datetime import datetime
from data_models import RawUnderlyingDataCombinedV2_5

def test_data_pipeline_root_cause():
    """Test to identify the root cause of missing data fields."""
    print("🔍 ROOT CAUSE ANALYSIS: Why are required fields None during live market hours?")
    print("=" * 80)
    
    # Test 1: Check what fields are actually required by ProcessedUnderlyingAggregatesV2_5
    print("\n1. 📋 CHECKING REQUIRED FIELDS IN ProcessedUnderlyingAggregatesV2_5")
    
    try:
        from data_models import ProcessedUnderlyingAggregatesV2_5
        
        print("Required fields that are causing the error:")
        required_fields = [
            'gib_oi_based_und', 'td_gib_und', 'hp_eod_und',
            'net_cust_delta_flow_und', 'net_cust_gamma_flow_und', 
            'net_cust_vega_flow_und', 'net_cust_theta_flow_und',
            'vapi_fa_z_score_und', 'dwfd_z_score_und', 'tw_laf_z_score_und',
            'elite_impact_score_und', 'institutional_flow_score_und', 
            'flow_momentum_index_und', 'market_regime_elite', 
            'flow_type_elite', 'volatility_regime_elite'
        ]
        
        for field in required_fields:
            if hasattr(ProcessedUnderlyingAggregatesV2_5, 'model_fields'):
                field_info = ProcessedUnderlyingAggregatesV2_5.model_fields.get(field)
                if field_info:
                    print(f"   ✅ {field}: {field_info.description}")
                else:
                    print(f"   ❌ {field}: FIELD NOT FOUND IN MODEL!")
            else:
                print(f"   ⚠️ Cannot access model_fields for ProcessedUnderlyingAggregatesV2_5")
                break
                
    except Exception as e:
        print(f"❌ Error checking ProcessedUnderlyingAggregatesV2_5: {e}")
        return False
    
    # Test 2: Check what ConvexValue data actually provides
    print("\n2. 📡 CHECKING CONVEXVALUE DATA STRUCTURE")
    
    try:
        # Create a sample ConvexValue data structure
        convex_data = RawUnderlyingDataCombinedV2_5(
            symbol="AAPL",
            timestamp=datetime.now(),
            price=150.0,
            # ConvexValue provides these fields
            u_volatility=0.25,
            day_volume=50000000,
            call_gxoi=1500.0,
            put_gxoi=-1200.0,
            value_bs=1000000.0,  # This maps to net_value_flow_5m_und
            volm_bs=25000.0,     # This maps to net_vol_flow_5m_und
            # But ConvexValue does NOT provide these calculated fields:
            price_change_abs_und=None,  # Needs Tradier enrichment
            price_change_pct_und=None,  # Needs Tradier enrichment
            # And definitely doesn't provide these calculated metrics:
            # gib_oi_based_und, td_gib_und, hp_eod_und, etc. - these need to be CALCULATED
        )
        
        print("ConvexValue provides:")
        print(f"   ✅ price: {convex_data.price}")
        print(f"   ✅ u_volatility: {convex_data.u_volatility}")
        print(f"   ✅ day_volume: {convex_data.day_volume}")
        print(f"   ✅ value_bs: {convex_data.value_bs} (raw flow data)")
        print(f"   ✅ volm_bs: {convex_data.volm_bs} (raw flow data)")
        
        print("ConvexValue does NOT provide (needs calculation):")
        print(f"   ❌ price_change_pct_und: {convex_data.price_change_pct_und}")
        print(f"   ❌ gib_oi_based_und: Not in ConvexValue data")
        print(f"   ❌ net_cust_delta_flow_und: Not in ConvexValue data")
        print(f"   ❌ elite_impact_score_und: Not in ConvexValue data")
        
    except Exception as e:
        print(f"❌ Error checking ConvexValue data: {e}")
        return False
    
    # Test 3: Identify the calculation pipeline issue
    print("\n3. 🔧 IDENTIFYING CALCULATION PIPELINE ISSUE")
    
    print("ROOT CAUSE IDENTIFIED:")
    print("=" * 50)
    print("❌ PROBLEM: The system expects ProcessedUnderlyingAggregatesV2_5 to have ALL calculated fields")
    print("❌ REALITY: ConvexValue only provides RAW data, not calculated metrics")
    print("❌ MISSING: The calculation pipeline that transforms raw data into calculated metrics")
    print()
    print("REQUIRED CALCULATION PIPELINE:")
    print("1. ConvexValue Raw Data → RawUnderlyingDataCombinedV2_5")
    print("2. Tradier Enrichment → Add price_change_pct_und, etc.")
    print("3. Foundational Metrics Calculation → Add gib_oi_based_und, td_gib_und, hp_eod_und")
    print("4. Flow Analytics Calculation → Add vapi_fa_z_score_und, dwfd_z_score_und, tw_laf_z_score_und")
    print("5. Elite Intelligence Calculation → Add elite_impact_score_und, institutional_flow_score_und")
    print("6. Final Model → ProcessedUnderlyingAggregatesV2_5 with ALL fields populated")
    print()
    print("CURRENT BROKEN PIPELINE:")
    print("1. ✅ ConvexValue Raw Data → RawUnderlyingDataCombinedV2_5")
    print("2. ⚠️ Tradier Enrichment → FIXED (price_change_pct_und now populated)")
    print("3. ❌ Foundational Metrics Calculation → MISSING/BROKEN")
    print("4. ❌ Flow Analytics Calculation → MISSING/BROKEN")
    print("5. ❌ Elite Intelligence Calculation → FAILS due to missing inputs")
    print("6. ❌ Final Model → NEVER CREATED")
    
    return True

def test_calculation_order_issue():
    """Test to identify the calculation order issue."""
    print("\n4. 🔄 CHECKING CALCULATION ORDER ISSUE")
    
    print("CALCULATION DEPENDENCY CHAIN:")
    print("=" * 40)
    print("Elite Intelligence REQUIRES:")
    print("   - net_vol_flow_5m_und (from flow analytics)")
    print("   - net_vol_flow_15m_und (from flow analytics)")
    print("   - net_vol_flow_30m_und (from flow analytics)")
    print("   - momentum_acceleration_index_und (from flow analytics)")
    print("   - All foundational metrics (gib_oi_based_und, etc.)")
    print()
    print("Flow Analytics REQUIRES:")
    print("   - Raw ConvexValue data (value_bs, volm_bs)")
    print("   - Basic underlying data (price, volume, volatility)")
    print()
    print("Foundational Metrics REQUIRES:")
    print("   - Raw ConvexValue data")
    print("   - Price change data (from Tradier enrichment)")
    print()
    print("CORRECT CALCULATION ORDER:")
    print("1. ✅ ConvexValue Data Fetch")
    print("2. ✅ Tradier Enrichment (FIXED)")
    print("3. ❌ Foundational Metrics Calculation (MISSING)")
    print("4. ❌ Flow Analytics Calculation (MISSING)")
    print("5. ❌ Elite Intelligence Calculation (FAILS)")
    
    return True

if __name__ == "__main__":
    print("🚀 ROOT CAUSE ANALYSIS: Data Pipeline Failure")
    print("=" * 80)
    
    test1_passed = test_data_pipeline_root_cause()
    test2_passed = test_calculation_order_issue()
    
    print("\n" + "=" * 80)
    print("📋 ROOT CAUSE ANALYSIS RESULTS:")
    print(f"✅ Data Structure Analysis: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"✅ Calculation Order Analysis: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎯 ROOT CAUSE IDENTIFIED:")
        print("The system is trying to create ProcessedUnderlyingAggregatesV2_5 directly")
        print("from raw ConvexValue data, but this model requires CALCULATED metrics.")
        print()
        print("SOLUTION REQUIRED:")
        print("1. Fix the calculation pipeline to run foundational metrics FIRST")
        print("2. Then run flow analytics to populate flow fields")
        print("3. Then run elite intelligence with all required inputs")
        print("4. ELIMINATE all _safe_float functions that mask this pipeline issue")
        print("5. Make the system FAIL FAST when required calculations are missing")
        sys.exit(0)
    else:
        print("\n❌ ROOT CAUSE ANALYSIS FAILED!")
        sys.exit(1)
