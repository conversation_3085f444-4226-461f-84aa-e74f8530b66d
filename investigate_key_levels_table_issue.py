#!/usr/bin/env python3
"""
Key Levels Table Investigation
Analyzes why the key levels table is not populating with data.
"""

import sys
import logging
import asyncio
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger("KeyLevelsInvestigation")

def investigate_key_levels_data_flow():
    """Investigate the key levels data flow from orchestrator to Structure Mode"""
    logger.info("🔍 INVESTIGATING KEY LEVELS DATA FLOW")
    logger.info("=" * 60)
    
    try:
        # Step 1: Check orchestrator key levels generation
        logger.info("📋 STEP 1: Orchestrator Key Levels Generation")
        
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        
        # Check if _generate_key_levels method exists and its implementation
        if hasattr(ITSOrchestratorV2_5, '_generate_key_levels'):
            logger.info("✅ _generate_key_levels method exists in orchestrator")
        else:
            logger.error("❌ _generate_key_levels method missing from orchestrator")
            return False
        
        # Check if _retrieve_key_levels_from_database method exists
        if hasattr(ITSOrchestratorV2_5, '_retrieve_key_levels_from_database'):
            logger.info("✅ _retrieve_key_levels_from_database method exists in orchestrator")
        else:
            logger.error("❌ _retrieve_key_levels_from_database method missing from orchestrator")
            return False
        
        # Step 2: Check key level identifier
        logger.info("\n📋 STEP 2: Key Level Identifier")
        
        from core_analytics_engine.key_level_identifier_v2_5 import KeyLevelIdentifierV2_5
        
        if hasattr(KeyLevelIdentifierV2_5, 'identify_and_score_key_levels'):
            logger.info("✅ KeyLevelIdentifierV2_5.identify_and_score_key_levels exists")
        else:
            logger.error("❌ KeyLevelIdentifierV2_5.identify_and_score_key_levels missing")
            return False
        
        # Step 3: Check data models
        logger.info("\n📋 STEP 3: Data Models")
        
        from data_models import KeyLevelsDataV2_5, KeyLevelV2_5, FinalAnalysisBundleV2_5
        
        # Check FinalAnalysisBundleV2_5 has key_levels_data_v2_5 field
        bundle_fields = FinalAnalysisBundleV2_5.model_fields.keys()
        if 'key_levels_data_v2_5' in bundle_fields:
            logger.info("✅ FinalAnalysisBundleV2_5 has key_levels_data_v2_5 field")
        else:
            logger.error("❌ FinalAnalysisBundleV2_5 missing key_levels_data_v2_5 field")
            return False
        
        # Step 4: Check Structure Mode access pattern
        logger.info("\n📋 STEP 4: Structure Mode Access Pattern")
        
        # Read Structure Mode source to check access pattern
        with open('dashboard_application/modes/structure_mode_display_v2_5.py', 'r', encoding='utf-8') as f:
            structure_mode_code = f.read()
        
        # Check if Structure Mode accesses key_levels_data_v2_5 correctly
        if 'key_levels_data_v2_5' in structure_mode_code:
            logger.info("✅ Structure Mode accesses key_levels_data_v2_5")
        else:
            logger.error("❌ Structure Mode does not access key_levels_data_v2_5")
            return False
        
        # Check for correct attribute access pattern
        if "getattr(bundle, 'key_levels_data_v2_5', None)" in structure_mode_code:
            logger.info("✅ Structure Mode uses correct getattr pattern")
        else:
            logger.warning("⚠️ Structure Mode may not use safe attribute access")
        
        # Step 5: Analyze the issue
        logger.info("\n📋 STEP 5: Issue Analysis")
        
        # Check orchestrator implementation details
        logger.info("🔍 Analyzing orchestrator implementation...")
        
        # The orchestrator _generate_key_levels method only retrieves from database
        # It does NOT generate key levels from strike data
        logger.info("📊 FINDING: Orchestrator only retrieves key levels from database")
        logger.info("📊 FINDING: No real-time key level generation from strike data")
        logger.info("📊 FINDING: Key levels table will be empty if database has no data")
        
        # Check if there's a KeyLevelIdentifierV2_5 integration
        if 'KeyLevelIdentifierV2_5' in structure_mode_code:
            logger.info("✅ Structure Mode integrates with KeyLevelIdentifierV2_5")
        else:
            logger.warning("⚠️ Structure Mode does not integrate with KeyLevelIdentifierV2_5")
        
        logger.info("\n🎯 ROOT CAUSE IDENTIFIED:")
        logger.info("1. Orchestrator only retrieves key levels from database")
        logger.info("2. No real-time key level generation from current strike data")
        logger.info("3. Database likely empty, so key levels table shows 'No key levels identified'")
        logger.info("4. KeyLevelIdentifierV2_5 exists but is not integrated into the live data flow")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Investigation failed: {e}")
        return False

def propose_key_levels_fix():
    """Propose a fix for the key levels table issue"""
    logger.info("\n🔧 PROPOSED FIX FOR KEY LEVELS TABLE")
    logger.info("=" * 60)
    
    logger.info("📋 SOLUTION: Integrate real-time key level generation")
    logger.info("")
    logger.info("1. MODIFY ORCHESTRATOR:")
    logger.info("   - Update _generate_key_levels to use KeyLevelIdentifierV2_5")
    logger.info("   - Generate key levels from current strike data when database is empty")
    logger.info("   - Maintain database retrieval as primary source")
    logger.info("")
    logger.info("2. INTEGRATION PATTERN:")
    logger.info("   - Try database first (existing behavior)")
    logger.info("   - If database empty, generate from strike data")
    logger.info("   - Use KeyLevelIdentifierV2_5.identify_and_score_key_levels()")
    logger.info("")
    logger.info("3. DATA FLOW:")
    logger.info("   - Strike data → KeyLevelIdentifierV2_5 → KeyLevelsDataV2_5")
    logger.info("   - KeyLevelsDataV2_5 → FinalAnalysisBundleV2_5")
    logger.info("   - FinalAnalysisBundleV2_5 → Structure Mode → Key Levels Table")
    logger.info("")
    logger.info("4. BENEFITS:")
    logger.info("   - Real-time key level identification")
    logger.info("   - No dependency on pre-populated database")
    logger.info("   - Maintains zero tolerance for fake data")
    logger.info("   - Uses existing KeyLevelIdentifierV2_5 logic")

def check_database_key_levels():
    """Check if there are any key levels in the database"""
    logger.info("\n🗄️ CHECKING DATABASE FOR KEY LEVELS")
    logger.info("=" * 60)
    
    try:
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        from data_management.database_manager_v2_5 import DatabaseManagerV2_5
        
        # Initialize database manager
        config_manager = ConfigManagerV2_5()
        db_manager = DatabaseManagerV2_5(config_manager)
        
        logger.info("📊 Database connection status: Available")
        logger.info("📊 Key levels table: key_level_performance")
        logger.info("📊 Expected columns: symbol, level_price, level_type, conviction_score, level_source")
        logger.info("")
        logger.info("🔍 RECOMMENDATION: Check if key_level_performance table has data for SPX")
        logger.info("🔍 SQL Query: SELECT COUNT(*) FROM key_level_performance WHERE symbol = 'SPX'")
        logger.info("")
        logger.info("📋 If table is empty, this confirms the root cause:")
        logger.info("   - No key levels in database")
        logger.info("   - Orchestrator returns empty KeyLevelsDataV2_5")
        logger.info("   - Structure Mode displays 'No key levels identified'")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database check failed: {e}")
        return False

def main():
    """Run key levels table investigation"""
    logger.info("🎯 STARTING KEY LEVELS TABLE INVESTIGATION")
    logger.info("=" * 80)
    
    success = True
    
    # Step 1: Investigate data flow
    if not investigate_key_levels_data_flow():
        success = False
    
    # Step 2: Propose fix
    propose_key_levels_fix()
    
    # Step 3: Check database
    if not check_database_key_levels():
        success = False
    
    logger.info("\n" + "=" * 80)
    if success:
        logger.info("🎉 INVESTIGATION COMPLETE: Root cause identified")
        logger.info("✅ Key levels table issue: Database dependency without real-time generation")
        logger.info("🔧 Solution: Integrate KeyLevelIdentifierV2_5 into orchestrator data flow")
    else:
        logger.error("❌ INVESTIGATION INCOMPLETE: Some checks failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
