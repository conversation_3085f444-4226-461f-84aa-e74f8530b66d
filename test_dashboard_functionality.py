#!/usr/bin/env python3
"""
Test Dashboard Functionality - EOTS v2.5
=========================================

This script tests the core dashboard functionality to verify:
1. Configuration parsing works with Pydantic v2 models
2. Dashboard modes can be imported and loaded
3. Live data pipeline flows without blockages
4. All components use strict Pydantic v2 architecture

Author: EOTS v2.5 Development Team
"""

import sys
import os
import logging
import asyncio
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_configuration_parsing():
    """Test that configuration parsing works with Pydantic v2 models."""
    logger.info("🔧 Testing configuration parsing...")
    
    try:
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        # Initialize configuration manager
        config_manager = ConfigManagerV2_5()
        
        # Verify config is loaded
        assert config_manager.config is not None, "Configuration not loaded"
        
        # Verify config is a Pydantic model
        assert hasattr(config_manager.config, 'model_dump'), "Config is not a Pydantic v2 model"
        
        # Test dashboard configuration access
        dashboard_config = config_manager.config.visualization_settings.dashboard
        assert hasattr(dashboard_config, 'model_dump'), "Dashboard config is not a Pydantic v2 model"
        
        # Test modes configuration access
        modes_config = dashboard_config.modes_detail_config
        assert hasattr(modes_config, 'model_dump'), "Modes config is not a Pydantic v2 model"
        
        # Verify specific modes exist
        assert hasattr(modes_config, 'main'), "Main mode not found in configuration"
        assert hasattr(modes_config, 'ai'), "AI mode not found in configuration"
        
        logger.info("✅ Configuration parsing test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration parsing test FAILED: {e}")
        return False

def test_dashboard_mode_imports():
    """Test that all dashboard modes can be imported correctly."""
    logger.info("📦 Testing dashboard mode imports...")
    
    mode_tests = {
        'main': 'dashboard_application.modes.main_dashboard_display_v2_5',
        'flow': 'dashboard_application.modes.flow_mode_display_v2_5',
        'structure': 'dashboard_application.modes.structure_mode_display_v2_5',
        'timedecay': 'dashboard_application.modes.time_decay_mode_display_v2_5',
        'volatility': 'dashboard_application.modes.volatility_mode_display_v2_5',
        'advanced': 'dashboard_application.modes.advanced_flow_mode_v2_5',
        'ai': 'dashboard_application.modes.ai_dashboard.ai_dashboard_display_v2_5'
    }
    
    success_count = 0
    
    for mode_name, module_path in mode_tests.items():
        try:
            # Import the module
            import importlib
            module = importlib.import_module(module_path)
            
            # Verify create_layout function exists
            assert hasattr(module, 'create_layout'), f"create_layout function not found in {module_path}"
            
            logger.info(f"✅ {mode_name} mode import PASSED")
            success_count += 1
            
        except Exception as e:
            logger.error(f"❌ {mode_name} mode import FAILED: {e}")
    
    if success_count == len(mode_tests):
        logger.info("✅ All dashboard mode imports test PASSED")
        return True
    else:
        logger.error(f"❌ Dashboard mode imports test FAILED: {success_count}/{len(mode_tests)} passed")
        return False

def test_pydantic_model_validation():
    """Test that core data models use Pydantic v2 architecture."""
    logger.info("🔍 Testing Pydantic v2 model validation...")
    
    try:
        from data_models import (
            FinalAnalysisBundleV2_5,
            ProcessedDataBundleV2_5,
            ProcessedUnderlyingAggregatesV2_5,
            DashboardModeSettings,
            DashboardModeCollection
        )
        
        # Test that models have Pydantic v2 methods
        models_to_test = [
            FinalAnalysisBundleV2_5,
            ProcessedDataBundleV2_5,
            ProcessedUnderlyingAggregatesV2_5,
            DashboardModeSettings,
            DashboardModeCollection
        ]
        
        for model_class in models_to_test:
            # Check for Pydantic v2 methods
            assert hasattr(model_class, 'model_validate'), f"{model_class.__name__} missing model_validate method"
            assert hasattr(model_class, 'model_dump'), f"{model_class.__name__} missing model_dump method"
            
            # Check for model_config attribute (Pydantic v2)
            assert hasattr(model_class, 'model_config'), f"{model_class.__name__} missing model_config attribute"
            
        logger.info("✅ Pydantic v2 model validation test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Pydantic v2 model validation test FAILED: {e}")
        return False

async def test_orchestrator_integration():
    """Test that the orchestrator can create valid data bundles."""
    logger.info("🔄 Testing orchestrator integration...")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        from data_models import FinalAnalysisBundleV2_5
        
        # Initialize components
        config_manager = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config_manager)
        
        # Test that orchestrator can create error bundles (fallback test)
        error_bundle = orchestrator._create_error_bundle("TEST", "Test error message")
        
        # Verify error bundle is a proper Pydantic model
        assert isinstance(error_bundle, FinalAnalysisBundleV2_5), "Error bundle is not FinalAnalysisBundleV2_5"
        assert hasattr(error_bundle, 'model_dump'), "Error bundle is not a Pydantic v2 model"
        
        # Test JSON serialization
        bundle_json = error_bundle.model_dump_json()
        assert isinstance(bundle_json, str), "Bundle JSON serialization failed"
        
        # Test JSON deserialization
        reconstructed_bundle = FinalAnalysisBundleV2_5.model_validate_json(bundle_json)
        assert isinstance(reconstructed_bundle, FinalAnalysisBundleV2_5), "Bundle JSON deserialization failed"
        
        logger.info("✅ Orchestrator integration test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Orchestrator integration test FAILED: {e}")
        return False

def main():
    """Run all dashboard functionality tests."""
    logger.info("🚀 Starting EOTS v2.5 Dashboard Functionality Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Configuration Parsing", test_configuration_parsing),
        ("Dashboard Mode Imports", test_dashboard_mode_imports),
        ("Pydantic v2 Model Validation", test_pydantic_model_validation),
        ("Orchestrator Integration", lambda: asyncio.run(test_orchestrator_integration()))
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 40)
        
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            logger.error(f"❌ {test_name} test encountered an error: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"🏁 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED - Dashboard functionality is working correctly!")
        return True
    else:
        logger.error("❌ SOME TESTS FAILED - Dashboard functionality needs attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
