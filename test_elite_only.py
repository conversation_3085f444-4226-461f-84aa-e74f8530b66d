# test_elite_only.py

"""
Test only the elite_intelligence module to isolate the import issue.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

def test_elite_intelligence_only():
    """Test importing only elite_intelligence"""
    print("🔍 Testing elite_intelligence import only...")
    
    try:
        # Add the specific path
        sys.path.insert(0, 'core_analytics_engine/eots_metrics')
        
        # Try to import just the module
        import elite_intelligence
        print("  ✅ elite_intelligence module imported successfully")
        
        # Try to import specific classes
        from elite_intelligence import EliteConfig
        print("  ✅ EliteConfig imported successfully")
        
        from elite_intelligence import EliteImpactCalculator
        print("  ✅ EliteImpactCalculator imported successfully")
        
        # Try to create instances
        config = EliteConfig()
        print("  ✅ EliteConfig instance created successfully")
        
        calculator = EliteImpactCalculator(config)
        print("  ✅ EliteImpactCalculator instance created successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Elite intelligence test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run elite intelligence only test"""
    print("🚀 Elite Intelligence Only Test\n")
    
    success = test_elite_intelligence_only()
    
    print("\n" + "=" * 60)
    print("📊 ELITE INTELLIGENCE TEST RESULTS")
    print("=" * 60)
    
    if success:
        print("🎉 ELITE INTELLIGENCE IMPORT SUCCESSFUL!")
        return True
    else:
        print("❌ Elite intelligence import failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
