# test_minimal_import.py

"""
Minimal test to isolate the elite_definitions import issue.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

def test_individual_imports():
    """Test each import individually to find the problematic one"""
    print("🔍 Testing Individual Imports...")
    
    try:
        print("  Testing elite_intelligence...")
        from core_analytics_engine.eots_metrics.elite_intelligence import EliteImpactCalculator, EliteConfig
        print("  ✅ elite_intelligence imports successfully")
    except Exception as e:
        print(f"  ❌ elite_intelligence failed: {e}")
        return False
    
    try:
        print("  Testing supplementary_metrics...")
        from core_analytics_engine.eots_metrics.supplementary_metrics import SupplementaryMetrics
        print("  ✅ supplementary_metrics imports successfully")
    except Exception as e:
        print(f"  ❌ supplementary_metrics failed: {e}")
        return False
    
    try:
        print("  Testing core_calculator...")
        from core_analytics_engine.eots_metrics.core_calculator import CoreCalculator
        print("  ✅ core_calculator imports successfully")
    except Exception as e:
        print(f"  ❌ core_calculator failed: {e}")
        return False
    
    try:
        print("  Testing flow_analytics...")
        from core_analytics_engine.eots_metrics.flow_analytics import FlowAnalytics
        print("  ✅ flow_analytics imports successfully")
    except Exception as e:
        print(f"  ❌ flow_analytics failed: {e}")
        return False
    
    try:
        print("  Testing adaptive_calculator...")
        from core_analytics_engine.eots_metrics.adaptive_calculator import AdaptiveCalculator
        print("  ✅ adaptive_calculator imports successfully")
    except Exception as e:
        print(f"  ❌ adaptive_calculator failed: {e}")
        return False
    
    try:
        print("  Testing visualization_metrics...")
        from core_analytics_engine.eots_metrics.visualization_metrics import VisualizationMetrics
        print("  ✅ visualization_metrics imports successfully")
    except Exception as e:
        print(f"  ❌ visualization_metrics failed: {e}")
        return False
    
    return True

def test_eots_metrics_init():
    """Test the eots_metrics __init__.py file"""
    print("🔍 Testing eots_metrics __init__.py...")
    
    try:
        from core_analytics_engine.eots_metrics import MetricsCalculatorV2_5
        print("  ✅ eots_metrics __init__ imports successfully")
        return True
    except Exception as e:
        print(f"  ❌ eots_metrics __init__ failed: {e}")
        return False

def main():
    """Run minimal import tests"""
    print("🚀 Minimal Import Test\n")
    
    success1 = test_individual_imports()
    print()
    success2 = test_eots_metrics_init()
    
    print("\n" + "=" * 60)
    print("📊 MINIMAL IMPORT TEST RESULTS")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 ALL IMPORTS SUCCESSFUL!")
        return True
    else:
        print("❌ Some imports failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
