#!/usr/bin/env python3
"""
Test script to debug elite metrics calculation
"""
import sys
import asyncio
sys.path.append('.')

from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
from utils.config_manager_v2_5 import ConfigManagerV2_5

async def test_elite_metrics():
    try:
        print("🔍 Initializing components...")
        config = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config)

        print("🔍 Testing elite metrics calculation...")
        result = await orchestrator.run_full_analysis_cycle('SPX', dte_min=0, dte_max=5, price_range_percent=5)
        
        und_data = result.processed_data_bundle.underlying_data_enriched
        print(f"✅ Elite Impact Score: {getattr(und_data, 'elite_impact_score_und', 'NOT_SET')}")
        print(f"✅ Flow Momentum Index: {getattr(und_data, 'flow_momentum_index_und', 'NOT_SET')}")
        print(f"✅ Institutional Flow Score: {getattr(und_data, 'institutional_flow_score_und', 'NOT_SET')}")
        print(f"✅ Market Regime Elite: {getattr(und_data, 'market_regime_elite', 'NOT_SET')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_elite_metrics())
    if success:
        print("🎉 Test completed successfully")
    else:
        print("💥 Test failed")
