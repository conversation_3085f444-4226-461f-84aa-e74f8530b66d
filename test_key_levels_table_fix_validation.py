#!/usr/bin/env python3
"""
KEY LEVELS TABLE FIX VALIDATION
===============================

Final validation test to confirm the Key Levels Table fix is working
and the table will be visible in the Structure Mode dashboard.
"""

import asyncio
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_enhanced_key_levels_table():
    """Test the enhanced key levels table with debugging and styling"""
    logger.info("🧪 TESTING: Enhanced Key Levels Table Fix")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        from dashboard_application.modes.structure_mode_display_v2_5 import _generate_key_level_table
        from dash import dash_table
        import dash_bootstrap_components as dbc
        
        # Initialize components
        config = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config)
        
        # Run analysis
        logger.info("🔄 Running analysis for SPX...")
        result = await orchestrator.run_full_analysis_cycle('SPX', dte_min=0, dte_max=5, price_range_percent=3)
        
        # Test the enhanced table generation
        logger.info("🔄 Testing enhanced key levels table generation...")
        table_component = _generate_key_level_table(result, config)
        
        # Validate the component structure
        logger.info(f"✅ Table component type: {type(table_component)}")
        
        if hasattr(table_component, 'children'):
            children = table_component.children
            logger.info(f"📊 Component has {len(children)} children")
            
            # Look for the DataTable specifically
            datatable_found = False
            for i, child in enumerate(children):
                logger.info(f"📊 Child {i}: {type(child)}")
                
                # Check for nested DataTable
                if hasattr(child, 'children'):
                    for j, nested_child in enumerate(child.children):
                        if isinstance(nested_child, dash_table.DataTable):
                            datatable_found = True
                            logger.info(f"🎯 FOUND DataTable in Child {i}, Nested Child {j}!")
                            logger.info(f"   DataTable ID: {getattr(nested_child, 'id', 'No ID')}")
                            logger.info(f"   DataTable data rows: {len(nested_child.data) if nested_child.data else 0}")
                            
                            if nested_child.data:
                                logger.info(f"   Sample data: {nested_child.data[0]}")
                            
                            # Check styling
                            style_cell = getattr(nested_child, 'style_cell', {})
                            style_table = getattr(nested_child, 'style_table', {})
                            logger.info(f"   Style cell: {style_cell}")
                            logger.info(f"   Style table: {style_table}")
                
                # Check for explicit styling on containers
                if hasattr(child, 'style'):
                    style = child.style
                    logger.info(f"   Child {i} style: {style}")
                    
                    # Verify visibility settings
                    if 'display' in style:
                        logger.info(f"   Display: {style['display']}")
                    if 'visibility' in style:
                        logger.info(f"   Visibility: {style['visibility']}")
            
            if not datatable_found:
                logger.error("❌ DataTable not found in component structure")
                return False
            
            logger.info("✅ DataTable found with proper structure and styling")
            
        # Check for explicit styling on the main component
        if hasattr(table_component, 'style'):
            main_style = table_component.style
            logger.info(f"📊 Main component style: {main_style}")
        
        logger.info("✅ Enhanced key levels table validation PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Enhanced table test error: {e}", exc_info=True)
        return False

async def test_structure_mode_integration():
    """Test the complete Structure Mode integration with enhanced table"""
    logger.info("🧪 TESTING: Structure Mode Integration with Enhanced Table")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        from dashboard_application.modes.structure_mode_display_v2_5 import create_layout
        
        # Initialize components
        config = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config)
        
        # Run analysis
        logger.info("🔄 Running analysis for SPX...")
        result = await orchestrator.run_full_analysis_cycle('SPX', dte_min=0, dte_max=5, price_range_percent=3)
        
        # Create the complete Structure Mode layout
        logger.info("🔄 Creating Structure Mode layout...")
        layout = create_layout(result, config)
        
        logger.info(f"✅ Layout created: {type(layout)}")
        
        # Verify the layout contains the key levels table
        if hasattr(layout, 'children'):
            container = layout.children[0]  # Should be dbc.Container
            if hasattr(container, 'children'):
                container_children = container.children
                
                # Look for the Row containing chart blocks
                for child in container_children:
                    if hasattr(child, 'children') and isinstance(child.children, list):
                        row_children = child.children
                        logger.info(f"📊 Found row with {len(row_children)} chart blocks")
                        
                        # The key levels table should be the 5th chart block (index 4)
                        if len(row_children) >= 5:
                            key_levels_col = row_children[4]  # 5th chart block
                            if hasattr(key_levels_col, 'children'):
                                key_levels_component = key_levels_col.children
                                logger.info(f"📊 Key levels component type: {type(key_levels_component)}")
                                
                                # Check if it has the enhanced styling
                                if hasattr(key_levels_component, 'style'):
                                    style = key_levels_component.style
                                    logger.info(f"📊 Key levels component style: {style}")
                                    
                                    # Verify visibility
                                    if style.get('display') == 'block' and style.get('visibility') == 'visible':
                                        logger.info("✅ Key levels component has proper visibility styling")
                                    else:
                                        logger.warning("⚠️ Key levels component may have visibility issues")
        
        logger.info("✅ Structure Mode integration validation PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Structure Mode integration test error: {e}", exc_info=True)
        return False

async def main():
    """Run all validation tests"""
    logger.info("🚀 KEY LEVELS TABLE FIX VALIDATION SUITE")
    logger.info("=" * 60)
    
    # Test 1: Enhanced table generation
    enhanced_table_success = await test_enhanced_key_levels_table()
    
    logger.info("=" * 60)
    
    # Test 2: Structure Mode integration
    integration_success = await test_structure_mode_integration()
    
    # Summary
    logger.info("=" * 60)
    logger.info("📊 VALIDATION RESULTS:")
    logger.info(f"✅ Enhanced Table Generation: {'PASS' if enhanced_table_success else 'FAIL'}")
    logger.info(f"✅ Structure Mode Integration: {'PASS' if integration_success else 'FAIL'}")
    
    if enhanced_table_success and integration_success:
        logger.info("🎉 ALL VALIDATIONS PASSED - Key Levels Table fix is successful!")
        logger.info("📋 The table should now be visible in the Structure Mode dashboard with:")
        logger.info("   • Explicit visibility styling (display: block, visibility: visible)")
        logger.info("   • Enhanced debugging logs for troubleshooting")
        logger.info("   • Proper DataTable ID for browser inspection")
        logger.info("   • Clear visual container with borders and background")
        logger.info("   • Row count indicator in the header")
        return True
    else:
        logger.error("💥 SOME VALIDATIONS FAILED - Additional fixes may be needed")
        return False

if __name__ == "__main__":
    asyncio.run(main())
