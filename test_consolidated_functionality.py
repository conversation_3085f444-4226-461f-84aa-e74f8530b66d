# test_consolidated_functionality.py

"""
Simple functionality test for consolidated EOTS metrics system.
Tests basic functionality without complex dependencies.
"""

import sys
import os
import traceback
from unittest.mock import Mock
import pandas as pd
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

def create_mock_managers():
    """Create mock managers for testing"""
    config_manager = Mock()
    historical_data_manager = Mock()
    enhanced_cache_manager = Mock()
    enhanced_cache_manager.get.return_value = None
    enhanced_cache_manager.set.return_value = None
    return config_manager, historical_data_manager, enhanced_cache_manager

def test_core_calculator():
    """Test CoreCalculator basic functionality"""
    print("🧪 Testing CoreCalculator...")
    
    try:
        from core_analytics_engine.eots_metrics.core_calculator import CoreCalculator
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Initialize calculator
        calculator = CoreCalculator(config_manager, historical_data_manager, enhanced_cache_manager)
        
        # Test sample data
        sample_data = {
            'symbol': 'SPY',
            'price': 450.0,
            'deltas_buy': 1000.0,
            'deltas_sell': 800.0,
            'gammas_call_buy': 500.0,
            'gammas_call_sell': 300.0,
            'gammas_put_buy': 400.0,
            'gammas_put_sell': 200.0,
            'vegas_buy': 2000.0,
            'vegas_sell': 1500.0,
            'thetas_buy': 800.0,
            'thetas_sell': 600.0,
            'call_gxoi': 15000.0,
            'put_gxoi': 18000.0
        }
        
        # Test foundational metrics calculation
        result = calculator.calculate_all_foundational_metrics(sample_data)
        
        # Verify results
        assert 'net_cust_delta_flow_und' in result
        assert 'net_cust_gamma_flow_und' in result
        assert 'gib_oi_based_und' in result
        assert 'hp_eod_und' in result
        
        print("✅ CoreCalculator test passed!")
        return True
        
    except Exception as e:
        print(f"❌ CoreCalculator test failed: {e}")
        traceback.print_exc()
        return False

def test_flow_analytics():
    """Test FlowAnalytics basic functionality"""
    print("🧪 Testing FlowAnalytics...")
    
    try:
        from core_analytics_engine.eots_metrics.flow_analytics import FlowAnalytics, FlowType
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Initialize calculator
        calculator = FlowAnalytics(config_manager, historical_data_manager, enhanced_cache_manager)
        
        # Test sample data
        sample_data = {
            'symbol': 'SPY',
            'net_value_flow_5m_und': 50000.0,
            'net_vol_flow_5m_und': 10000.0,
            'net_vol_flow_15m_und': 25000.0,
            'net_vol_flow_30m_und': 40000.0,
            'u_volatility': 0.20,
            'day_volume': 30000000
        }
        
        # Test flow type classification
        flow_type = calculator._classify_flow_type_optimized(sample_data)
        assert isinstance(flow_type, FlowType)
        
        # Test momentum calculation
        momentum = calculator._calculate_momentum_acceleration_index_optimized(sample_data)
        assert isinstance(momentum, (int, float))
        
        print("✅ FlowAnalytics test passed!")
        return True
        
    except Exception as e:
        print(f"❌ FlowAnalytics test failed: {e}")
        traceback.print_exc()
        return False

def test_adaptive_calculator():
    """Test AdaptiveCalculator basic functionality"""
    print("🧪 Testing AdaptiveCalculator...")
    
    try:
        from core_analytics_engine.eots_metrics.adaptive_calculator import AdaptiveCalculator, MarketRegime
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Initialize calculator
        calculator = AdaptiveCalculator(config_manager, historical_data_manager, enhanced_cache_manager)
        
        # Test sample data
        sample_underlying_data = {
            'u_volatility': 0.25,
            'price_change_pct_und': 0.015,
            'day_volume': 25000000
        }
        
        sample_strike_data = pd.DataFrame({
            'strike': [440, 445, 450, 455, 460],
            'dte': [7, 7, 7, 7, 7],
            'total_gxoi_at_strike': [1000, 1500, 2000, 1500, 1000],
            'total_dxoi_at_strike': [500, 750, 1000, 750, 500],
            'total_vxoi_at_strike': [200, 300, 400, 300, 200],
            'total_txoi_at_strike': [100, 150, 200, 150, 100]
        })
        
        # Test regime detection
        regime = calculator._determine_market_regime_optimized(sample_underlying_data, sample_strike_data)
        assert isinstance(regime, MarketRegime)
        
        # Test volatility regime
        vol_regime = calculator._determine_volatility_regime_optimized(sample_underlying_data, sample_strike_data)
        assert isinstance(vol_regime, str)
        
        print("✅ AdaptiveCalculator test passed!")
        return True
        
    except Exception as e:
        print(f"❌ AdaptiveCalculator test failed: {e}")
        traceback.print_exc()
        return False

def test_visualization_metrics():
    """Test VisualizationMetrics basic functionality"""
    print("🧪 Testing VisualizationMetrics...")
    
    try:
        from core_analytics_engine.eots_metrics.visualization_metrics import VisualizationMetrics
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Initialize calculator
        calculator = VisualizationMetrics(config_manager, historical_data_manager, enhanced_cache_manager)
        
        # Test sample data
        sample_strike_data = pd.DataFrame({
            'strike': [440, 445, 450, 455, 460],
            'total_gxoi_at_strike': [1000, 1500, 2000, 1500, 1000],
            'total_dxoi_at_strike': [500, 750, 1000, 750, 500],
            'total_vxoi_at_strike': [200, 300, 400, 300, 200],
            'nvp_at_strike': [10000, 15000, 20000, 15000, 10000],
            'nvp_vol_at_strike': [5000, 7500, 10000, 7500, 5000]
        })
        
        sample_underlying_data = {
            'symbol': 'SPY',
            'u_volatility': 0.20
        }
        
        # Test underlying aggregates calculation
        aggregates = calculator.calculate_all_underlying_aggregates(sample_strike_data, sample_underlying_data)
        assert isinstance(aggregates, dict)
        assert 'total_dxoi_und' in aggregates
        assert 'total_gxoi_und' in aggregates
        
        print("✅ VisualizationMetrics test passed!")
        return True
        
    except Exception as e:
        print(f"❌ VisualizationMetrics test failed: {e}")
        traceback.print_exc()
        return False

def test_elite_intelligence():
    """Test EliteIntelligence basic functionality"""
    print("🧪 Testing EliteIntelligence...")
    
    try:
        from core_analytics_engine.eots_metrics.elite_intelligence import EliteImpactCalculator, EliteConfig
        
        # Initialize calculator
        elite_config = EliteConfig()
        calculator = EliteImpactCalculator(elite_config)
        
        # Test sample data
        sample_options_data = pd.DataFrame({
            'strike': [440, 445, 450, 455, 460],
            'volume': [1000, 1500, 2000, 1500, 1000]
        })
        
        sample_underlying_data = {
            'net_value_flow_5m_und': 50000.0,
            'net_vol_flow_5m_und': 10000.0,
            'day_volume': 30000000,
            'u_volatility': 0.25,
            'price_change_pct_und': 0.015
        }
        
        # Test elite impact calculation
        result = calculator.calculate_elite_impact_score(sample_options_data, sample_underlying_data)
        assert isinstance(result, dict)
        assert 'elite_impact_score_und' in result
        assert 'institutional_flow_score_und' in result
        assert 'market_regime_elite' in result
        
        print("✅ EliteIntelligence test passed!")
        return True
        
    except Exception as e:
        print(f"❌ EliteIntelligence test failed: {e}")
        traceback.print_exc()
        return False

def test_supplementary_metrics():
    """Test SupplementaryMetrics basic functionality"""
    print("🧪 Testing SupplementaryMetrics...")
    
    try:
        from core_analytics_engine.eots_metrics.supplementary_metrics import SupplementaryMetrics, AdvancedOptionsMetrics
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Mock historical data manager for ATR calculation
        historical_data_manager.get_historical_ohlcv.return_value = pd.DataFrame({
            'high': [451, 452, 453, 454, 455],
            'low': [449, 450, 451, 452, 453],
            'close': [450, 451, 452, 453, 454]
        })
        
        # Initialize calculator
        calculator = SupplementaryMetrics(config_manager, historical_data_manager, enhanced_cache_manager)
        
        # Test ATR calculation
        atr = calculator.calculate_atr('SPY', dte_max=30)
        assert isinstance(atr, (int, float))
        
        # Test advanced options metrics
        sample_options_data = pd.DataFrame({
            'strike': [440, 445, 450, 455, 460],
            'volume': [1000, 1500, 2000, 1500, 1000]
        })
        
        sample_underlying_data = {
            'day_volume': 30000000,
            'net_value_flow_5m_und': 50000.0,
            'net_vol_flow_5m_und': 10000.0,
            'u_volatility': 0.25
        }
        
        advanced_metrics = calculator.calculate_advanced_options_metrics(sample_options_data, sample_underlying_data)
        assert isinstance(advanced_metrics, AdvancedOptionsMetrics)
        
        print("✅ SupplementaryMetrics test passed!")
        return True
        
    except Exception as e:
        print(f"❌ SupplementaryMetrics test failed: {e}")
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """Test MetricsCalculatorV2_5 backward compatibility"""
    print("🧪 Testing Backward Compatibility...")
    
    try:
        from core_analytics_engine.eots_metrics import MetricsCalculatorV2_5
        
        # Create mock managers
        config_manager, historical_data_manager, enhanced_cache_manager = create_mock_managers()
        
        # Initialize composite calculator
        calculator = MetricsCalculatorV2_5(config_manager, historical_data_manager, enhanced_cache_manager)
        
        # Test initialization
        assert hasattr(calculator, 'core')
        assert hasattr(calculator, 'flow_analytics')
        assert hasattr(calculator, 'adaptive')
        assert hasattr(calculator, 'visualization')
        assert hasattr(calculator, 'elite_intelligence')
        assert hasattr(calculator, 'supplementary')
        
        # Test backward compatibility aliases
        assert hasattr(calculator, 'foundational')
        assert hasattr(calculator, 'enhanced_flow')
        assert hasattr(calculator, 'heatmap')
        assert hasattr(calculator, 'underlying_aggregates')
        assert hasattr(calculator, 'miscellaneous')
        assert hasattr(calculator, 'elite_impact')
        
        # Verify aliases point to correct modules
        assert calculator.foundational is calculator.core
        assert calculator.enhanced_flow is calculator.flow_analytics
        assert calculator.heatmap is calculator.visualization
        
        print("✅ Backward Compatibility test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Backward Compatibility test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Consolidated Metrics Functionality Tests\n")
    
    tests = [
        test_core_calculator,
        test_flow_analytics,
        test_adaptive_calculator,
        test_visualization_metrics,
        test_elite_intelligence,
        test_supplementary_metrics,
        test_backward_compatibility
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Consolidated system is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
