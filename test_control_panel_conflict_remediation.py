#!/usr/bin/env python3
"""
Comprehensive validation testing for control panel conflict remediation.
Tests that control panel parameters are the single source of truth throughout the system.
"""

import sys
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger("ControlPanelConflictValidation")

def test_callback_manager_no_fallbacks():
    """Test 1: Verify callback manager has no fallback logic"""
    logger.info("🧪 TEST 1: Callback Manager Fallback Elimination")
    
    try:
        # Read callback manager source code
        with open('dashboard_application/callback_manager_v2_5.py', 'r', encoding='utf-8') as f:
            callback_code = f.read()
        
        # Check for prohibited fallback patterns
        prohibited_patterns = [
            'symbol or "SPY"',
            'dte_min or 0',
            'dte_max or 5',
            'price_range_percent or 5',
            'refresh_interval or 30',
            'interval_seconds else 60'
        ]
        
        violations = []
        for pattern in prohibited_patterns:
            if pattern in callback_code:
                violations.append(pattern)
        
        if violations:
            logger.error(f"❌ FAILED: Found fallback patterns: {violations}")
            return False
        
        # Check for required validation patterns
        required_patterns = [
            'CRITICAL: Symbol required from control panel',
            'CRITICAL: DTE minimum required from control panel',
            'CRITICAL: DTE maximum required from control panel',
            'CRITICAL: Price range percentage required from control panel',
            'CRITICAL: Refresh interval required from control panel'
        ]
        
        missing_validations = []
        for pattern in required_patterns:
            if pattern not in callback_code:
                missing_validations.append(pattern)
        
        if missing_validations:
            logger.error(f"❌ FAILED: Missing validation patterns: {missing_validations}")
            return False
        
        logger.info("✅ PASSED: Callback manager has no fallback logic")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Error testing callback manager - {e}")
        return False

def test_data_fetcher_no_defaults():
    """Test 2: Verify data fetchers have no default parameters"""
    logger.info("🧪 TEST 2: Data Fetcher Default Parameter Elimination")
    
    try:
        # Test ConvexValue data fetcher
        with open('data_management/convexvalue_data_fetcher_v2_5.py', 'r', encoding='utf-8') as f:
            convex_code = f.read()

        # Check for prohibited default parameters
        if 'dte_min: int = 0' in convex_code or 'dte_max: int = 45' in convex_code or 'price_range_percent: int = 20' in convex_code:
            logger.error("❌ FAILED: ConvexValue fetcher still has default parameters")
            return False

        # Check for required validation
        if 'CRITICAL: Symbol required from control panel' not in convex_code:
            logger.error("❌ FAILED: ConvexValue fetcher missing parameter validation")
            return False

        # Test Tradier data fetcher
        with open('data_management/tradier_data_fetcher_v2_5.py', 'r', encoding='utf-8') as f:
            tradier_code = f.read()
        
        # Check for prohibited default parameters
        if 'days: int = 30' in tradier_code:
            logger.error("❌ FAILED: Tradier fetcher still has default parameters")
            return False
        
        logger.info("✅ PASSED: Data fetchers have no default parameters")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Error testing data fetchers - {e}")
        return False

def test_layout_manager_no_hardcoded_fallbacks():
    """Test 3: Verify layout manager has no hardcoded fallbacks"""
    logger.info("🧪 TEST 3: Layout Manager Hardcoded Fallback Elimination")
    
    try:
        with open('dashboard_application/layout_manager_v2_5.py', 'r', encoding='utf-8') as f:
            layout_code = f.read()
        
        # Check for prohibited hardcoded fallbacks (not UI initialization)
        # These patterns indicate problematic fallback logic, not UI initialization
        prohibited_fallbacks = [
            "# Fallback values",  # The comment that indicates fallback logic
            "default_refresh = 30",  # Hardcoded refresh without config
            "default_dte_max = 45",  # Old hardcoded value (should be 5 from config)
            "default_price_range = 20"  # Old hardcoded value (should be 5 from config)
        ]
        
        violations = []
        for fallback in prohibited_fallbacks:
            if fallback in layout_code:
                violations.append(fallback)
        
        if violations:
            logger.error(f"❌ FAILED: Found hardcoded fallbacks: {violations}")
            return False
        
        # Check for required fail-fast behavior and proper validation
        required_validations = [
            'CRITICAL: Configuration loading failed',
            'CRITICAL: Get config values with strict validation',
            'UI initialization only'  # Comment indicating these are for UI, not data processing
        ]

        missing_validations = []
        for validation in required_validations:
            if validation not in layout_code:
                missing_validations.append(validation)

        if missing_validations:
            logger.error(f"❌ FAILED: Layout manager missing validations: {missing_validations}")
            return False
        
        logger.info("✅ PASSED: Layout manager has no hardcoded fallbacks")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Error testing layout manager - {e}")
        return False

def test_intraday_collector_integration():
    """Test 4: Verify intraday collector integrates with control panel"""
    logger.info("🧪 TEST 4: Intraday Collector Control Panel Integration")
    
    try:
        # Test IntradayCollectorSettings model
        from data_models.core_system_config import IntradayCollectorSettings, ControlPanelStateV2_5
        
        # Create test control panel state
        control_panel_state = ControlPanelStateV2_5(
            symbol="QQQ",
            dte_min=1,
            dte_max=3,
            price_range_percent=8,
            refresh_interval_seconds=45
        )
        
        # Create intraday collector settings
        collector_settings = IntradayCollectorSettings(
            watched_tickers=["SPY", "QQQ"],
            metrics=["vapi_fa", "dwfd", "tw_laf"],
            cache_dir="cache/intraday_metrics",
            collection_interval_seconds=5,
            market_open_time="09:30:00",
            market_close_time="16:00:00",
            reset_at_eod=True,
            metrics_to_collect=None,
            reset_cache_at_eod=None,
            symbol=None,
            dte_min=None,
            dte_max=None,
            fetch_interval_seconds=None
        )
        
        # Test control panel integration
        updated_settings = collector_settings.update_from_control_panel(control_panel_state)
        
        # Verify parameters were updated correctly
        assert updated_settings.symbol == "QQQ"
        assert updated_settings.dte_min == 1
        assert updated_settings.dte_max == 3
        assert updated_settings.fetch_interval_seconds == 45
        
        logger.info("✅ PASSED: Intraday collector integrates with control panel")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Error testing intraday collector integration - {e}")
        return False

def test_end_to_end_parameter_flow():
    """Test 5: Verify end-to-end parameter flow without substitution"""
    logger.info("🧪 TEST 5: End-to-End Parameter Flow Validation")
    
    try:
        from data_models.core_system_config import ControlPanelStateV2_5
        
        # Create test control panel state with specific values
        test_params = {
            "symbol": "QQQ",
            "dte_min": 1,
            "dte_max": 3,
            "price_range_percent": 8,
            "refresh_interval_seconds": 45
        }
        
        control_panel_state = ControlPanelStateV2_5(**test_params)
        
        # Test serialization/deserialization (callback flow)
        state_json = control_panel_state.model_dump_json()
        restored_state = ControlPanelStateV2_5.model_validate_json(state_json)
        
        # Verify no parameter substitution occurred
        assert restored_state.symbol == test_params["symbol"]
        assert restored_state.dte_min == test_params["dte_min"]
        assert restored_state.dte_max == test_params["dte_max"]
        assert restored_state.price_range_percent == test_params["price_range_percent"]
        assert restored_state.refresh_interval_seconds == test_params["refresh_interval_seconds"]
        
        logger.info("✅ PASSED: End-to-end parameter flow maintains integrity")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Error testing end-to-end parameter flow - {e}")
        return False

def main():
    """Run comprehensive control panel conflict remediation validation"""
    logger.info("🎯 STARTING COMPREHENSIVE CONTROL PANEL CONFLICT REMEDIATION VALIDATION")
    logger.info("=" * 80)
    
    tests = [
        test_callback_manager_no_fallbacks,
        test_data_fetcher_no_defaults,
        test_layout_manager_no_hardcoded_fallbacks,
        test_intraday_collector_integration,
        test_end_to_end_parameter_flow
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            logger.error(f"❌ CRITICAL ERROR in {test.__name__}: {e}")
            results.append(False)
        
        logger.info("-" * 40)
    
    # Summary
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    logger.info("=" * 80)
    logger.info(f"🎯 VALIDATION SUMMARY: {passed}/{total} tests passed ({success_rate:.1f}%)")
    
    if success_rate == 100:
        logger.info("🎉 SUCCESS: All control panel conflicts have been remediated!")
        logger.info("✅ Control panel parameters are now the single source of truth")
        return True
    else:
        logger.error("❌ FAILURE: Control panel conflicts still exist")
        logger.error("🚨 System does not meet zero-tolerance requirements")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
