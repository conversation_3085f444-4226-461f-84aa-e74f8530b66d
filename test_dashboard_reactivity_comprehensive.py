#!/usr/bin/env python3
"""
Comprehensive Dashboard Reactivity Investigation
Tests all aspects of dashboard interactivity and identifies root causes of non-responsive behavior.
"""

import sys
import logging
import requests
import time
import json
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger("DashboardReactivityTest")

def test_dashboard_accessibility():
    """Test 1: Verify dashboard is accessible and responsive"""
    logger.info("🧪 TEST 1: Dashboard Accessibility and Response")
    
    try:
        # Test basic accessibility
        response = requests.get("http://localhost:8050", timeout=10)
        
        if response.status_code != 200:
            logger.error(f"❌ FAILED: Dashboard returned status {response.status_code}")
            return False
        
        # Check if response contains expected Dash components
        content = response.text
        expected_components = [
            'dash-renderer',
            'dash-bootstrap-components',
            'main-data-store-id',
            'manual-refresh-button-id',
            'symbol-input-id',
            'page-content-id'
        ]
        
        missing_components = []
        for component in expected_components:
            if component not in content:
                missing_components.append(component)
        
        if missing_components:
            logger.error(f"❌ FAILED: Missing dashboard components: {missing_components}")
            return False
        
        logger.info("✅ PASSED: Dashboard is accessible with all expected components")
        logger.info(f"📊 Response size: {len(content)} bytes")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Dashboard accessibility error - {e}")
        return False

def test_callback_registration():
    """Test 2: Verify callback registration is complete"""
    logger.info("🧪 TEST 2: Callback Registration Verification")
    
    try:
        from dashboard_application.callback_manager_v2_5 import register_v2_5_callbacks
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        import dash
        
        # Create test app and components
        test_app = dash.Dash(__name__)
        config_manager = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config_manager)
        
        # Test callback registration
        register_v2_5_callbacks(test_app, orchestrator, config_manager)
        
        # Check if callbacks are registered
        callback_count = len(test_app.callback_map)
        
        if callback_count == 0:
            logger.error("❌ FAILED: No callbacks registered")
            return False
        
        logger.info(f"✅ PASSED: {callback_count} callbacks registered successfully")
        
        # Check for specific critical callbacks
        critical_callback_patterns = [
            'main-data-store-id',
            'page-content-id',
            'manual-refresh-button-id'
        ]
        
        registered_callbacks = str(test_app.callback_map)
        missing_callbacks = []
        for pattern in critical_callback_patterns:
            if pattern not in registered_callbacks:
                missing_callbacks.append(pattern)
        
        if missing_callbacks:
            logger.warning(f"⚠️ WARNING: Some critical callbacks may be missing: {missing_callbacks}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Callback registration error - {e}")
        return False

def test_component_id_consistency():
    """Test 3: Verify component IDs are consistent between layout and callbacks"""
    logger.info("🧪 TEST 3: Component ID Consistency")
    
    try:
        # Check IDs module
        from dashboard_application import ids
        
        # Critical IDs that must exist
        critical_ids = [
            'ID_MAIN_DATA_STORE',
            'ID_MANUAL_REFRESH_BUTTON',
            'ID_SYMBOL_INPUT',
            'ID_PAGE_CONTENT',
            'ID_URL_LOCATION',
            'ID_INTERVAL_LIVE_UPDATE',
            'ID_REFRESH_INTERVAL_DROPDOWN'
        ]
        
        missing_ids = []
        for id_name in critical_ids:
            if not hasattr(ids, id_name):
                missing_ids.append(id_name)
        
        if missing_ids:
            logger.error(f"❌ FAILED: Missing critical IDs: {missing_ids}")
            return False
        
        # Check if callback manager uses these IDs
        with open('dashboard_application/callback_manager_v2_5.py', 'r', encoding='utf-8') as f:
            callback_code = f.read()
        
        # Verify IDs are used in callbacks
        used_ids = []
        for id_name in critical_ids:
            id_value = getattr(ids, id_name)
            if id_value in callback_code:
                used_ids.append(id_name)
        
        if len(used_ids) != len(critical_ids):
            logger.warning(f"⚠️ WARNING: Not all IDs are used in callbacks. Used: {len(used_ids)}/{len(critical_ids)}")
        
        logger.info(f"✅ PASSED: Component ID consistency verified ({len(used_ids)}/{len(critical_ids)} IDs used)")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Component ID consistency error - {e}")
        return False

def test_control_panel_component_structure():
    """Test 4: Verify control panel components have correct structure"""
    logger.info("🧪 TEST 4: Control Panel Component Structure")
    
    try:
        from dashboard_application.layout_manager_v2_5 import create_control_panel
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        # Create control panel
        config_manager = ConfigManagerV2_5()
        control_panel = create_control_panel(config_manager)
        
        # Convert to dict to inspect structure
        control_panel_dict = control_panel.to_plotly_json()
        
        # Check for required input components
        required_components = [
            'symbol-input-id',
            'dte-min-input',
            'dte-max-input',
            'price-range-input',
            'manual-refresh-button-id',
            'refresh-interval-dropdown-id'
        ]
        
        # Recursively search for components in the structure
        def find_components(obj, found_components=None):
            if found_components is None:
                found_components = set()
            
            if isinstance(obj, dict):
                if 'props' in obj and 'id' in obj['props']:
                    found_components.add(obj['props']['id'])
                for value in obj.values():
                    find_components(value, found_components)
            elif isinstance(obj, list):
                for item in obj:
                    find_components(item, found_components)
            
            return found_components
        
        found_components = find_components(control_panel_dict)
        
        missing_components = []
        for component in required_components:
            if component not in found_components:
                missing_components.append(component)
        
        if missing_components:
            logger.error(f"❌ FAILED: Missing control panel components: {missing_components}")
            logger.info(f"📊 Found components: {sorted(found_components)}")
            return False
        
        logger.info(f"✅ PASSED: All required control panel components found")
        logger.info(f"📊 Total components: {len(found_components)}")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Control panel structure error - {e}")
        return False

def test_mode_navigation_structure():
    """Test 5: Verify mode navigation structure"""
    logger.info("🧪 TEST 5: Mode Navigation Structure")
    
    try:
        from dashboard_application.layout_manager_v2_5 import create_header
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        # Create header with navigation
        config_manager = ConfigManagerV2_5()
        header = create_header(config_manager)
        
        # Convert to dict to inspect structure
        header_dict = header.to_plotly_json()
        
        # Check for navigation links
        def find_nav_links(obj, nav_links=None):
            if nav_links is None:
                nav_links = []
            
            if isinstance(obj, dict):
                if obj.get('type') == 'NavLink' and 'props' in obj:
                    href = obj['props'].get('href', '')
                    children = obj['props'].get('children', '')
                    nav_links.append({'href': href, 'label': children})
                for value in obj.values():
                    find_nav_links(value, nav_links)
            elif isinstance(obj, list):
                for item in obj:
                    find_nav_links(item, nav_links)
            
            return nav_links
        
        nav_links = find_nav_links(header_dict)
        
        # Expected navigation modes
        expected_modes = ['/', '/flow', '/volatility', '/structure', '/timedecay', '/advanced', '/ai']
        
        found_hrefs = [link['href'] for link in nav_links]
        missing_modes = []
        for mode in expected_modes:
            if mode not in found_hrefs:
                missing_modes.append(mode)
        
        if missing_modes:
            logger.warning(f"⚠️ WARNING: Some navigation modes missing: {missing_modes}")
        
        logger.info(f"✅ PASSED: Mode navigation structure verified")
        logger.info(f"📊 Navigation links found: {len(nav_links)}")
        for link in nav_links:
            logger.info(f"  - {link['label']}: {link['href']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Mode navigation structure error - {e}")
        return False

def test_data_store_initialization():
    """Test 6: Verify data stores are properly initialized"""
    logger.info("🧪 TEST 6: Data Store Initialization")
    
    try:
        from dashboard_application.layout_manager_v2_5 import create_master_layout
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        # Create master layout
        config_manager = ConfigManagerV2_5()
        layout = create_master_layout(config_manager)
        
        # Convert to dict to inspect structure
        layout_dict = layout.to_plotly_json()
        
        # Check for data stores
        def find_stores(obj, stores=None):
            if stores is None:
                stores = []
            
            if isinstance(obj, dict):
                if obj.get('type') == 'Store' and 'props' in obj:
                    store_id = obj['props'].get('id', '')
                    storage_type = obj['props'].get('storage_type', '')
                    stores.append({'id': store_id, 'storage_type': storage_type})
                for value in obj.values():
                    find_stores(value, stores)
            elif isinstance(obj, list):
                for item in obj:
                    find_stores(item, stores)
            
            return stores
        
        stores = find_stores(layout_dict)
        
        # Expected stores
        expected_stores = ['main-data-store-id', 'control-panel-state-store']
        
        found_store_ids = [store['id'] for store in stores]
        missing_stores = []
        for store_id in expected_stores:
            if store_id not in found_store_ids:
                missing_stores.append(store_id)
        
        if missing_stores:
            logger.error(f"❌ FAILED: Missing data stores: {missing_stores}")
            return False
        
        logger.info(f"✅ PASSED: Data stores properly initialized")
        for store in stores:
            logger.info(f"  - {store['id']}: {store['storage_type']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Data store initialization error - {e}")
        return False

def analyze_dashboard_logs():
    """Test 7: Analyze dashboard logs for errors"""
    logger.info("🧪 TEST 7: Dashboard Log Analysis")
    
    try:
        # This would normally read from the dashboard terminal
        # For now, we'll check for common error patterns in the code
        
        logger.info("📊 Dashboard is running - check terminal for:")
        logger.info("  - Callback execution messages")
        logger.info("  - Error messages when buttons are clicked")
        logger.info("  - Mode switching logs")
        logger.info("  - Data fetching status updates")
        
        logger.info("✅ PASSED: Log analysis framework ready")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Dashboard log analysis error - {e}")
        return False

def main():
    """Run comprehensive dashboard reactivity investigation"""
    logger.info("🎯 STARTING COMPREHENSIVE DASHBOARD REACTIVITY INVESTIGATION")
    logger.info("=" * 80)
    
    tests = [
        test_dashboard_accessibility,
        test_callback_registration,
        test_component_id_consistency,
        test_control_panel_component_structure,
        test_mode_navigation_structure,
        test_data_store_initialization,
        analyze_dashboard_logs
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            logger.error(f"❌ CRITICAL ERROR in {test.__name__}: {e}")
            results.append(False)
        
        logger.info("-" * 40)
    
    # Summary
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    logger.info("=" * 80)
    logger.info(f"🎯 DASHBOARD REACTIVITY INVESTIGATION: {passed}/{total} tests passed ({success_rate:.1f}%)")
    
    if success_rate >= 85:
        logger.info("🎉 SUCCESS: Dashboard structure appears correct!")
        logger.info("✅ All critical components are properly configured")
        logger.info("✅ Callbacks are registered and IDs are consistent")
        logger.info("✅ Control panel and navigation structures are valid")
        logger.info("")
        logger.info("🔍 NEXT STEPS FOR REACTIVITY ISSUES:")
        logger.info("1. Check browser developer console for JavaScript errors")
        logger.info("2. Monitor dashboard terminal for callback execution logs")
        logger.info("3. Test actual button clicks and mode navigation in browser")
        logger.info("4. Verify network requests are being made to the dashboard")
        logger.info("5. Check if Dash is properly handling client-server communication")
    else:
        logger.error("❌ ISSUES DETECTED: Dashboard structure has problems")
        logger.error("🔧 Review failed tests and fix structural issues first")
    
    return success_rate >= 85

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
