#!/usr/bin/env python3
"""
Test script to verify Pydantic v2 fixes are working
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from core_analytics_engine.eots_metrics.flow_analytics import FlowAnalytics
from core_analytics_engine.eots_metrics.core_calculator import FoundationalMetricsOutput
from unittest.mock import Mock

def test_pydantic_fix():
    """Test that the Pydantic v2 fixes are working"""
    print("Testing Pydantic v2 fixes...")

    # Create a sample FoundationalMetricsOutput object
    test_data = FoundationalMetricsOutput(
        symbol="SPX",
        price=5800.0,
        gib_oi_based_und=0.0,
        hp_eod_und=0.0,
        td_gib_und=0.0,
        net_cust_delta_flow_und=1000.0,
        net_cust_gamma_flow_und=500.0,
        net_cust_vega_flow_und=200.0,
        net_cust_theta_flow_und=-100.0
    )

    print(f"Created test data: {type(test_data)}")
    print(f"Test data symbol: {test_data.symbol}")

    # Create FlowAnalytics instance with mock dependencies
    mock_config = Mock()
    mock_historical = Mock()
    mock_cache = Mock()
    flow_analytics = FlowAnalytics(mock_config, mock_historical, mock_cache)
    
    try:
        # This should now work without errors and return a Pydantic model
        result = flow_analytics.calculate_all_enhanced_flow_metrics(test_data, "SPX")
        print(f"✅ SUCCESS: Flow analytics returned: {type(result)}")
        print(f"Result symbol: {result.symbol}")
        print(f"Has flow_type_elite: {hasattr(result, 'flow_type_elite')}")
        print(f"Has vapi_fa_z_score_und: {hasattr(result, 'vapi_fa_z_score_und')}")
        return True
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pydantic_fix()
    if success:
        print("\n🎉 Pydantic v2 fixes are working correctly!")
    else:
        print("\n💥 Pydantic v2 fixes are NOT working!")
    sys.exit(0 if success else 1)
