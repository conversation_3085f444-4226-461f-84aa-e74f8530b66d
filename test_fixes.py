# test_fixes.py

"""
Simple test to verify that both issues have been fixed:
1. Pydantic v1/v2 conflict in expert_ai_config.py
2. ConvexLib dependency availability
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

def test_pydantic_fix():
    """Test that Pydantic conflict is fixed"""
    print("🧪 Testing Pydantic v1/v2 conflict fix...")

    try:
        from data_models.expert_ai_config import AnalyticsEngineConfigV2_5, MOESystemConfig
        print("  ✅ expert_ai_config.py imports successfully")

        # Try to create instances
        analytics_config = AnalyticsEngineConfigV2_5()
        print("  ✅ AnalyticsEngineConfigV2_5 instance created successfully")

        moe_config = MOESystemConfig()
        print("  ✅ MOESystemConfig instance created successfully")

        # Test model_dump (v2 syntax)
        config_dict = analytics_config.model_dump()
        print("  ✅ model_dump() works (Pydantic v2 syntax)")

        return True

    except Exception as e:
        print(f"  ❌ Pydantic test failed: {e}")
        return False

def test_convexlib_fix():
    """Test that ConvexLib is available"""
    print("🧪 Testing ConvexLib availability...")
    
    try:
        import convexlib
        print("  ✅ convexlib imports successfully")
        
        # Test basic functionality if available
        if hasattr(convexlib, '__version__'):
            print(f"  ✅ ConvexLib version: {convexlib.__version__}")
        else:
            print("  ✅ ConvexLib imported (version info not available)")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ ConvexLib import failed: {e}")
        return False
    except Exception as e:
        print(f"  ❌ ConvexLib test failed: {e}")
        return False

def test_processed_data_fix():
    """Test that processed_data.py is available"""
    print("🧪 Testing processed_data.py availability...")
    
    try:
        from data_models import ProcessedDataBundleV2_5, ProcessedUnderlyingAggregatesV2_5
        print("  ✅ processed_data.py imports successfully")
        
        # Try to create an instance
        bundle = ProcessedDataBundleV2_5(
            processed_underlying=ProcessedUnderlyingAggregatesV2_5(symbol="SPY")
        )
        print("  ✅ ProcessedDataBundleV2_5 instance created successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ processed_data test failed: {e}")
        return False

def test_elite_intelligence_import():
    """Test that elite_intelligence can be imported (depends on both fixes)"""
    print("🧪 Testing elite_intelligence import (integration test)...")
    
    try:
        # Direct import to avoid complex dependencies
        sys.path.insert(0, 'core_analytics_engine/eots_metrics')
        from elite_intelligence import EliteImpactCalculator, EliteConfig
        print("  ✅ elite_intelligence imports successfully")
        
        # Try to create an instance
        config = EliteConfig()
        calculator = EliteImpactCalculator(config)
        print("  ✅ EliteImpactCalculator instance created successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ elite_intelligence test failed: {e}")
        return False

def main():
    """Run all fix tests"""
    print("🚀 Testing Issue Fixes\n")
    
    tests = [
        ("Pydantic v1/v2 Conflict Fix", test_pydantic_fix),
        ("ConvexLib Dependency Fix", test_convexlib_fix),
        ("Processed Data Availability", test_processed_data_fix),
        ("Elite Intelligence Integration", test_elite_intelligence_import)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"📋 {test_name}")
        try:
            if test_func():
                passed += 1
                print("  🎉 PASSED\n")
            else:
                failed += 1
                print("  ❌ FAILED\n")
        except Exception as e:
            print(f"  💥 CRASHED: {e}\n")
            failed += 1
    
    print("=" * 60)
    print("📊 FIX TEST RESULTS")
    print("=" * 60)
    print(f"✅ Passed: {passed}/4")
    print(f"❌ Failed: {failed}/4")
    
    if failed == 0:
        print("\n🎉 ALL FIXES SUCCESSFUL!")
        print("✅ Pydantic conflict resolved")
        print("✅ ConvexLib dependency available")
        print("✅ System ready for full functionality testing")
        return True
    else:
        print(f"\n⚠️  {failed} fix(es) still have issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
