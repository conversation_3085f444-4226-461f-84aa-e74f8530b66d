#!/usr/bin/env python3
"""
Key Levels Orchestrator Fix Validation
Tests the enhanced orchestrator key levels generation without complex model validation.
"""

import sys
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger("KeyLevelsOrchestratorFix")

def test_orchestrator_helper_methods():
    """Test 1: Verify orchestrator helper methods work correctly"""
    logger.info("🧪 TEST 1: Orchestrator Helper Methods")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from data_models import KeyLevelsDataV2_5, KeyLevelV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        # Create test orchestrator instance
        config_manager = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config_manager)
        
        # Test empty key levels with required timestamp
        empty_levels = KeyLevelsDataV2_5(
            supports=[], resistances=[], pin_zones=[], vol_triggers=[], major_walls=[],
            timestamp=datetime.now()
        )
        
        assert orchestrator._count_total_levels(empty_levels) == 0
        assert not orchestrator._has_sufficient_key_levels(empty_levels)
        
        # Test sufficient key levels with all required fields
        test_levels = KeyLevelsDataV2_5(
            supports=[
                KeyLevelV2_5(
                    level_price=4500.0, 
                    level_type="Support", 
                    conviction_score=0.8,
                    contributing_metrics=["a_mspi"],
                    source_identifier="test_source"
                ),
                KeyLevelV2_5(
                    level_price=4480.0, 
                    level_type="Support", 
                    conviction_score=0.7,
                    contributing_metrics=["nvp"],
                    source_identifier="test_source"
                )
            ],
            resistances=[
                KeyLevelV2_5(
                    level_price=4520.0, 
                    level_type="Resistance", 
                    conviction_score=0.9,
                    contributing_metrics=["sgdhp"],
                    source_identifier="test_source"
                )
            ],
            pin_zones=[],
            vol_triggers=[],
            major_walls=[],
            timestamp=datetime.now()
        )
        
        assert orchestrator._count_total_levels(test_levels) == 3
        assert orchestrator._has_sufficient_key_levels(test_levels)
        
        logger.info("✅ PASSED: Helper methods work correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Helper methods test error - {e}")
        return False

def test_key_level_identifier_exists():
    """Test 2: Verify KeyLevelIdentifierV2_5 can be imported and instantiated"""
    logger.info("🧪 TEST 2: KeyLevelIdentifierV2_5 Availability")
    
    try:
        from core_analytics_engine.key_level_identifier_v2_5 import KeyLevelIdentifierV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        # Create test data
        config_manager = ConfigManagerV2_5()
        identifier = KeyLevelIdentifierV2_5(config_manager)
        
        # Verify the identifier has the required method
        assert hasattr(identifier, 'identify_and_score_key_levels')
        
        logger.info("✅ PASSED: KeyLevelIdentifierV2_5 available and has required methods")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: KeyLevelIdentifierV2_5 availability error - {e}")
        return False

def test_enhanced_generate_key_levels_method():
    """Test 3: Verify enhanced _generate_key_levels method exists"""
    logger.info("🧪 TEST 3: Enhanced _generate_key_levels Method")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        # Create test orchestrator
        config_manager = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config_manager)
        
        # Verify the enhanced method exists
        assert hasattr(orchestrator, '_generate_key_levels')
        assert hasattr(orchestrator, '_has_sufficient_key_levels')
        assert hasattr(orchestrator, '_count_total_levels')
        
        # Check method signature (should be async)
        import inspect
        method = getattr(orchestrator, '_generate_key_levels')
        assert inspect.iscoroutinefunction(method)
        
        logger.info("✅ PASSED: Enhanced _generate_key_levels method exists and is async")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Enhanced _generate_key_levels method error - {e}")
        return False

def test_orchestrator_imports():
    """Test 4: Verify orchestrator can import required modules"""
    logger.info("🧪 TEST 4: Orchestrator Import Dependencies")
    
    try:
        # Test that the orchestrator file contains the required imports
        with open('core_analytics_engine/its_orchestrator_v2_5.py', 'r', encoding='utf-8') as f:
            orchestrator_code = f.read()
        
        # Check for the real-time key level generation code
        required_patterns = [
            'from core_analytics_engine.key_level_identifier_v2_5 import KeyLevelIdentifierV2_5',
            'import pandas as pd',
            'key_level_identifier = KeyLevelIdentifierV2_5(self.config_manager)',
            'real_time_levels = key_level_identifier.identify_and_score_key_levels',
            'def _has_sufficient_key_levels',
            'def _count_total_levels'
        ]
        
        missing_patterns = []
        for pattern in required_patterns:
            if pattern not in orchestrator_code:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            logger.error(f"❌ FAILED: Missing required patterns: {missing_patterns}")
            return False
        
        logger.info("✅ PASSED: Orchestrator has all required imports and code patterns")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Orchestrator import dependencies error - {e}")
        return False

def test_structure_mode_key_levels_access():
    """Test 5: Verify Structure Mode can access key levels correctly"""
    logger.info("🧪 TEST 5: Structure Mode Key Levels Access")
    
    try:
        # Check Structure Mode source for correct access pattern
        with open('dashboard_application/modes/structure_mode_display_v2_5.py', 'r', encoding='utf-8') as f:
            structure_mode_code = f.read()
        
        # Check for correct key levels access patterns
        required_patterns = [
            'key_levels_data_v2_5',
            'getattr(bundle, \'key_levels_data_v2_5\', None)',
            'No key levels identified',
            '_generate_key_level_table'
        ]
        
        missing_patterns = []
        for pattern in required_patterns:
            if pattern not in structure_mode_code:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            logger.error(f"❌ FAILED: Missing Structure Mode patterns: {missing_patterns}")
            return False
        
        logger.info("✅ PASSED: Structure Mode has correct key levels access patterns")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Structure Mode key levels access error - {e}")
        return False

def test_pydantic_models_basic_validation():
    """Test 6: Verify basic Pydantic model validation works"""
    logger.info("🧪 TEST 6: Basic Pydantic Model Validation")
    
    try:
        from data_models import KeyLevelsDataV2_5, KeyLevelV2_5
        
        # Test KeyLevelV2_5 creation with all required fields
        key_level = KeyLevelV2_5(
            level_price=4500.0,
            level_type="Support",
            conviction_score=0.85,
            contributing_metrics=["a_mspi", "nvp"],
            source_identifier="test_validation"
        )
        
        assert key_level.level_price == 4500.0
        assert key_level.level_type == "Support"
        assert key_level.conviction_score == 0.85
        
        # Test KeyLevelsDataV2_5 creation with timestamp
        key_levels_data = KeyLevelsDataV2_5(
            supports=[key_level],
            resistances=[],
            pin_zones=[],
            vol_triggers=[],
            major_walls=[],
            timestamp=datetime.now()
        )
        
        assert len(key_levels_data.supports) == 1
        assert len(key_levels_data.resistances) == 0
        assert key_levels_data.timestamp is not None
        
        logger.info("✅ PASSED: Basic Pydantic model validation working")
        return True
        
    except Exception as e:
        logger.error(f"❌ FAILED: Basic Pydantic model validation error - {e}")
        return False

def main():
    """Run key levels orchestrator fix validation"""
    logger.info("🎯 STARTING KEY LEVELS ORCHESTRATOR FIX VALIDATION")
    logger.info("=" * 80)
    
    tests = [
        test_orchestrator_helper_methods,
        test_key_level_identifier_exists,
        test_enhanced_generate_key_levels_method,
        test_orchestrator_imports,
        test_structure_mode_key_levels_access,
        test_pydantic_models_basic_validation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            logger.error(f"❌ CRITICAL ERROR in {test.__name__}: {e}")
            results.append(False)
        
        logger.info("-" * 40)
    
    # Summary
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    logger.info("=" * 80)
    logger.info(f"🎯 ORCHESTRATOR FIX VALIDATION: {passed}/{total} tests passed ({success_rate:.1f}%)")
    
    if success_rate == 100:
        logger.info("🎉 SUCCESS: Key levels orchestrator fix validation completed successfully!")
        logger.info("✅ Enhanced orchestrator is ready for real-time key level generation")
        logger.info("🔧 Structure Mode key levels table should now populate with data")
        logger.info("📊 Next step: Test with live dashboard to verify end-to-end functionality")
    else:
        logger.error("❌ ISSUES DETECTED: Some aspects need attention")
        logger.error("🔧 Review failed tests and implement necessary fixes")
    
    return success_rate == 100

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
