#!/usr/bin/env python3
"""
Test script to check if ConvexValue is returning DTE and volatility fields correctly.
"""

import asyncio
import sys
sys.path.append('.')
from data_management.convexvalue_data_fetcher_v2_5 import ConvexValueDataFetcherV2_5
from utils.config_manager_v2_5 import ConfigManagerV2_5

async def test_dte_fields():
    """Test DTE and volatility field extraction from ConvexValue"""

    # Initialize config manager
    config_manager = ConfigManagerV2_5()
    fetcher = ConvexValueDataFetcherV2_5(config_manager)
    
    print('🔌 Testing ConvexValue DTE and volatility field calculation...')
    
    # Fetch options chain data
    options_contracts, underlying_data = await fetcher.fetch_chain_and_underlying(
        session=None, 
        symbol='SPX', 
        dte_min=0, 
        dte_max=5
    )
    
    if options_contracts:
        print(f'📊 Found {len(options_contracts)} contracts')
        print()
        
        # Check first few contracts for DTE and volatility fields
        for i, contract in enumerate(options_contracts[:5]):
            print(f'Contract {i+1}:')
            print(f'  Strike: {contract.strike}')
            print(f'  Option Type: {contract.opt_kind}')
            print(f'  DTE_CALC: {getattr(contract, "dte_calc", "MISSING")}')
            print(f'  IV: {getattr(contract, "iv", "MISSING")}')
            print(f'  Raw Price: {getattr(contract, "raw_price", "MISSING")}')
            print(f'  Contract Symbol: {contract.contract_symbol}')
            print('---')

        # Check if all contracts have DTE
        contracts_with_dte = sum(1 for c in options_contracts if hasattr(c, 'dte_calc') and c.dte_calc is not None)
        contracts_with_iv = sum(1 for c in options_contracts if hasattr(c, 'iv') and c.iv is not None)
        contracts_with_price = sum(1 for c in options_contracts if hasattr(c, 'raw_price') and c.raw_price is not None)
        
        print(f'📈 Summary:')
        print(f'  Total contracts: {len(options_contracts)}')
        print(f'  Contracts with DTE_CALC: {contracts_with_dte}')
        print(f'  Contracts with IV: {contracts_with_iv}')
        print(f'  Contracts with raw_price: {contracts_with_price}')
        
        # Test underlying data
        if underlying_data:
            print(f'\n📊 Underlying data for {underlying_data.symbol}:')
            print(f'  Price: {underlying_data.price}')
            print(f'  Volatility: {getattr(underlying_data, "u_volatility", "MISSING")}')
            print(f'  Value_BS: {getattr(underlying_data, "value_bs", "MISSING")}')
            print(f'  Volm_BS: {getattr(underlying_data, "volm_bs", "MISSING")}')
        
    else:
        print('❌ No contracts returned')

if __name__ == "__main__":
    asyncio.run(test_dte_fields())
