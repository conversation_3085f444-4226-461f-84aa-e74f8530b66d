#!/usr/bin/env python3
"""
STRUCTURE MODE FIXES VALIDATION TEST
====================================

Comprehensive test to validate the fixes for:
1. E-SDAG missing variants (directional, weighted, vol flow)
2. Key Levels Table empty data issue

This test performs end-to-end validation of the Structure Mode dashboard
data pipeline to ensure both issues are resolved.
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_e_sdag_variants_fix():
    """Test 1: Validate E-SDAG variants are properly calculated"""
    logger.info("🧪 TEST 1: E-SDAG Variants Calculation")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        # Initialize components
        config = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config)
        
        # Run analysis for SPX
        logger.info("🔄 Running full analysis cycle for SPX...")
        result = await orchestrator.run_full_analysis_cycle('SPX', dte_min=0, dte_max=5, price_range_percent=10)
        
        # Extract strike data
        strike_data = result.processed_data_bundle.strike_level_data_with_metrics
        if not strike_data:
            logger.error("❌ FAILED: No strike data available")
            return False
        
        # Convert to DataFrame for analysis
        df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
        
        # Check for all four E-SDAG variants
        required_esdag_columns = [
            'e_sdag_mult_strike',
            'e_sdag_dir_strike', 
            'e_sdag_w_strike',
            'e_sdag_vf_strike'
        ]
        
        missing_columns = []
        for col in required_esdag_columns:
            if col not in df_strike.columns:
                missing_columns.append(col)
            else:
                # Check if column has real data (not all zeros/nulls)
                non_zero_count = (df_strike[col].fillna(0) != 0).sum()
                logger.info(f"✅ {col}: {non_zero_count}/{len(df_strike)} strikes have non-zero values")
                
                if non_zero_count == 0:
                    logger.warning(f"⚠️ {col} exists but all values are zero/null")
        
        if missing_columns:
            logger.error(f"❌ FAILED: Missing E-SDAG columns: {missing_columns}")
            return False
        
        # Validate that variants have different values (not identical)
        mult_values = df_strike['e_sdag_mult_strike'].fillna(0)
        dir_values = df_strike['e_sdag_dir_strike'].fillna(0)
        w_values = df_strike['e_sdag_w_strike'].fillna(0)
        vf_values = df_strike['e_sdag_vf_strike'].fillna(0)
        
        # Check if variants are different (not identical calculations)
        variants_identical = (
            mult_values.equals(dir_values) and 
            mult_values.equals(w_values) and 
            mult_values.equals(vf_values)
        )
        
        if variants_identical:
            logger.warning("⚠️ WARNING: All E-SDAG variants have identical values - may indicate calculation issue")
        else:
            logger.info("✅ E-SDAG variants have different values as expected")
        
        # Log sample values for verification
        sample_idx = len(df_strike) // 2  # Middle strike
        if sample_idx < len(df_strike):
            sample_strike = df_strike.iloc[sample_idx]['strike']
            logger.info(f"📊 Sample E-SDAG values at strike {sample_strike}:")
            logger.info(f"   Multiplicative: {mult_values.iloc[sample_idx]:.4f}")
            logger.info(f"   Directional: {dir_values.iloc[sample_idx]:.4f}")
            logger.info(f"   Weighted: {w_values.iloc[sample_idx]:.4f}")
            logger.info(f"   Vol Flow: {vf_values.iloc[sample_idx]:.4f}")
        
        logger.info("✅ TEST 1 PASSED: E-SDAG variants calculation successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ TEST 1 FAILED: E-SDAG variants test error - {e}")
        return False

async def test_key_levels_generation():
    """Test 2: Validate Key Levels are properly generated and attached to bundle"""
    logger.info("🧪 TEST 2: Key Levels Generation and Data Pipeline")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        from data_models import KeyLevelsDataV2_5
        
        # Initialize components
        config = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config)
        
        # Run analysis for SPX
        logger.info("🔄 Running full analysis cycle for SPX...")
        result = await orchestrator.run_full_analysis_cycle('SPX', dte_min=0, dte_max=5, price_range_percent=10)
        
        # Check if key_levels_data_v2_5 exists in bundle
        key_levels_data = getattr(result, 'key_levels_data_v2_5', None)
        if not key_levels_data:
            logger.error("❌ FAILED: key_levels_data_v2_5 not found in bundle")
            return False
        
        if not isinstance(key_levels_data, KeyLevelsDataV2_5):
            logger.error(f"❌ FAILED: key_levels_data_v2_5 is not KeyLevelsDataV2_5 type: {type(key_levels_data)}")
            return False
        
        # Check individual level types
        supports = getattr(key_levels_data, 'supports', [])
        resistances = getattr(key_levels_data, 'resistances', [])
        pin_zones = getattr(key_levels_data, 'pin_zones', [])
        vol_triggers = getattr(key_levels_data, 'vol_triggers', [])
        major_walls = getattr(key_levels_data, 'major_walls', [])
        
        total_levels = len(supports) + len(resistances) + len(pin_zones) + len(vol_triggers) + len(major_walls)
        
        logger.info(f"📊 Key Levels Summary:")
        logger.info(f"   Supports: {len(supports)}")
        logger.info(f"   Resistances: {len(resistances)}")
        logger.info(f"   Pin Zones: {len(pin_zones)}")
        logger.info(f"   Vol Triggers: {len(vol_triggers)}")
        logger.info(f"   Major Walls: {len(major_walls)}")
        logger.info(f"   Total Levels: {total_levels}")
        
        if total_levels == 0:
            logger.error("❌ FAILED: No key levels generated")
            
            # Debug: Check strike data availability
            strike_data = result.processed_data_bundle.strike_level_data_with_metrics
            if not strike_data:
                logger.error("❌ DEBUG: No strike data available for key level generation")
                return False
            
            # Debug: Check required columns in strike data
            df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
            logger.info(f"📊 DEBUG: Strike data shape: {df_strike.shape}")
            logger.info(f"📊 DEBUG: Available columns: {list(df_strike.columns)}")
            
            # Check for SGDHP scores specifically
            if 'sgdhp_score_strike' in df_strike.columns:
                sgdhp_values = df_strike['sgdhp_score_strike'].fillna(0)
                non_zero_sgdhp = (sgdhp_values != 0).sum()
                logger.info(f"📊 DEBUG: SGDHP scores - {non_zero_sgdhp}/{len(df_strike)} non-zero values")
                logger.info(f"📊 DEBUG: SGDHP range: {sgdhp_values.min():.4f} to {sgdhp_values.max():.4f}")
            else:
                logger.error("❌ DEBUG: sgdhp_score_strike column missing from strike data")
            
            return False
        
        # Validate individual key levels have required fields
        if supports:
            sample_support = supports[0]
            required_fields = ['level_price', 'level_type', 'conviction_score', 'contributing_metrics', 'source_identifier']
            for field in required_fields:
                if not hasattr(sample_support, field):
                    logger.error(f"❌ FAILED: Key level missing required field: {field}")
                    return False
            
            logger.info(f"✅ Sample support level: {sample_support.level_price} (conviction: {sample_support.conviction_score})")
        
        logger.info("✅ TEST 2 PASSED: Key levels generation successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ TEST 2 FAILED: Key levels test error - {e}")
        return False

async def test_structure_mode_display():
    """Test 3: Validate Structure Mode display components work with fixed data"""
    logger.info("🧪 TEST 3: Structure Mode Display Integration")
    
    try:
        from core_analytics_engine.its_orchestrator_v2_5 import ITSOrchestratorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        from dashboard_application.modes.structure_mode_display_v2_5 import _generate_esdag_charts, _generate_key_level_table
        
        # Initialize components
        config = ConfigManagerV2_5()
        orchestrator = ITSOrchestratorV2_5(config)
        
        # Run analysis for SPX
        logger.info("🔄 Running full analysis cycle for SPX...")
        result = await orchestrator.run_full_analysis_cycle('SPX', dte_min=0, dte_max=5, price_range_percent=10)
        
        # Test E-SDAG chart generation
        logger.info("🔄 Testing E-SDAG chart generation...")
        esdag_chart = _generate_esdag_charts(result, config)
        if esdag_chart is None:
            logger.error("❌ FAILED: E-SDAG chart generation returned None")
            return False
        
        logger.info("✅ E-SDAG chart generated successfully")
        
        # Test Key Levels table generation
        logger.info("🔄 Testing Key Levels table generation...")
        key_levels_table = _generate_key_level_table(result, config)
        if key_levels_table is None:
            logger.error("❌ FAILED: Key Levels table generation returned None")
            return False
        
        logger.info("✅ Key Levels table generated successfully")
        
        logger.info("✅ TEST 3 PASSED: Structure Mode display integration successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ TEST 3 FAILED: Structure Mode display test error - {e}")
        return False

async def main():
    """Run all validation tests"""
    logger.info("🚀 STRUCTURE MODE FIXES VALIDATION TEST SUITE")
    logger.info("=" * 60)
    
    test_results = []
    
    # Run all tests
    test_results.append(await test_e_sdag_variants_fix())
    test_results.append(await test_key_levels_generation())
    test_results.append(await test_structure_mode_display())
    
    # Summary
    logger.info("=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY:")
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    logger.info(f"✅ Passed: {passed_tests}/{total_tests}")
    logger.info(f"❌ Failed: {total_tests - passed_tests}/{total_tests}")
    
    if all(test_results):
        logger.info("🎉 ALL TESTS PASSED - Structure Mode fixes validated successfully!")
        return True
    else:
        logger.error("💥 SOME TESTS FAILED - Structure Mode fixes need additional work")
        return False

if __name__ == "__main__":
    asyncio.run(main())
