#!/usr/bin/env python3
"""
Test script to check ConvexValue API field mapping and availability.
"""

import asyncio
import sys
import os
sys.path.append('.')

from data_management.convexvalue_data_fetcher_v2_5 import ConvexValueDataFetcherV2_5
from utils.config_manager_v2_5 import ConfigManagerV2_5

async def test_convexvalue_fields():
    """Test ConvexValue API to see exactly what fields are returned"""
    
    print("🔌 Testing ConvexValue API field mapping...")
    
    # Initialize config manager and fetcher
    config_manager = ConfigManagerV2_5()
    fetcher = ConvexValueDataFetcherV2_5(config_manager)
    
    try:
        # Test underlying data
        print("\n📊 TESTING UNDERLYING DATA:")
        print("=" * 50)
        
        options_contracts, underlying_data = await fetcher.fetch_chain_and_underlying(
            session=None, 
            symbol='SPX', 
            dte_min=0, 
            dte_max=5
        )
        
        if underlying_data:
            print(f"✅ Underlying data retrieved for SPX")
            print(f"📋 Underlying data fields:")
            
            # Get all fields from the underlying data model
            underlying_fields = underlying_data.model_dump()
            for field_name, field_value in underlying_fields.items():
                print(f"   {field_name}: {field_value} (type: {type(field_value).__name__})")
        else:
            print("❌ No underlying data retrieved")
        
        # Test options chain data
        print(f"\n📈 TESTING OPTIONS CHAIN DATA:")
        print("=" * 50)
        
        if options_contracts and len(options_contracts) > 0:
            print(f"✅ Retrieved {len(options_contracts)} options contracts")
            
            # Look at first contract to see available fields
            first_contract = options_contracts[0]
            print(f"📋 First contract fields:")
            
            contract_fields = first_contract.model_dump()
            for field_name, field_value in contract_fields.items():
                print(f"   {field_name}: {field_value} (type: {type(field_value).__name__})")
            
            # Check for specific fields we're looking for
            print(f"\n🔍 CHECKING SPECIFIC FIELDS:")
            print("=" * 30)

            # Check DTE field
            if hasattr(first_contract, 'dte_calc'):
                print(f"✅ DTE field found: dte_calc = {first_contract.dte_calc}")
            else:
                print("❌ DTE field (dte_calc) not found")

            # Check implied volatility field
            if hasattr(first_contract, 'iv'):
                print(f"✅ Implied volatility field found: iv = {first_contract.iv}")
            else:
                print("❌ Implied volatility field (iv) not found")

            # Check volume field (CRITICAL FOR GREEK FLOWS)
            if hasattr(first_contract, 'volm'):
                print(f"📊 Volume field found: volm = {first_contract.volm}")
            else:
                print("❌ Volume field (volm) not found")

            # Check Greek fields
            greek_fields = ['delta_contract', 'gamma_contract', 'vega_contract', 'theta_contract']
            for field in greek_fields:
                if hasattr(first_contract, field):
                    value = getattr(first_contract, field)
                    print(f"✅ Greek field found: {field} = {value}")
                else:
                    print(f"❌ Greek field ({field}) not found")

            # Check 5m flow fields
            flow_fields = ['valuebs_5m', 'volmbs_5m', 'valuebs_15m', 'volmbs_15m']
            for field in flow_fields:
                if hasattr(first_contract, field):
                    value = getattr(first_contract, field)
                    print(f"✅ Flow field found: {field} = {value}")
                else:
                    print(f"❌ Flow field ({field}) not found")
            
            # Check for any fields containing 'dte' or 'volatility' or 'flow'
            print(f"\n🔍 SEARCHING FOR RELATED FIELDS:")
            print("=" * 35)
            
            all_fields = list(contract_fields.keys())
            
            # Search for DTE-related fields
            dte_fields = [f for f in all_fields if 'dte' in f.lower()]
            if dte_fields:
                print(f"📅 DTE-related fields: {dte_fields}")
            
            # Search for volatility-related fields
            vol_fields = [f for f in all_fields if 'vol' in f.lower() or 'iv' in f.lower()]
            if vol_fields:
                print(f"📈 Volatility-related fields: {vol_fields}")
            
            # Search for flow-related fields
            flow_fields = [f for f in all_fields if 'flow' in f.lower() or 'value' in f.lower() or '5m' in f.lower()]
            if flow_fields:
                print(f"🌊 Flow-related fields: {flow_fields}")
                
        else:
            print("❌ No options contracts retrieved")
            
    except Exception as e:
        print(f"❌ Error testing ConvexValue fields: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_convexvalue_fields())
