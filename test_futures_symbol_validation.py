#!/usr/bin/env python3
"""
FUTURES SYMBOL VALIDATION TEST
==============================

Comprehensive test to validate that the updated symbol validation
in ControlPanelStateV2_5 and ControlPanelParametersV2_5 properly
supports futures symbols while maintaining data integrity.
"""

import logging
from typing import List, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_control_panel_state_futures_symbols():
    """Test ControlPanelStateV2_5 with futures symbols"""
    logger.info("🧪 TESTING: ControlPanelStateV2_5 Futures Symbol Support")
    
    try:
        from data_models.core_system_config import ControlPanelStateV2_5
        
        # Test cases: (symbol, should_pass, description)
        test_cases: List[Tuple[str, bool, str]] = [
            # Valid equity symbols
            ("SPX", True, "Standard equity symbol"),
            ("SPY", True, "Standard equity symbol"),
            ("QQQ", True, "Standard equity symbol"),
            ("AAPL", True, "Standard equity symbol"),
            ("TSLA", True, "Standard equity symbol"),
            
            # Valid futures symbols
            ("/ES:XCME", True, "E-mini S&P 500 futures"),
            ("/NQ:XCME", True, "E-mini NASDAQ futures"),
            ("/YM:XCME", True, "E-mini Dow futures"),
            ("/RTY:XCME", True, "E-mini Russell 2000 futures"),
            ("/CL:NYMEX", True, "Crude oil futures"),
            ("/GC:COMEX", True, "Gold futures"),
            ("/SI:COMEX", True, "Silver futures"),
            ("/ZB:CBOT", True, "30-year Treasury bond futures"),
            
            # Invalid symbols (should fail)
            ("", False, "Empty symbol"),
            ("   ", False, "Whitespace only"),
            ("SPX123", False, "Equity with numbers"),
            ("SP@X", False, "Equity with special chars"),
            ("/", False, "Just forward slash"),
            ("/ES", False, "Futures without exchange"),
            ("/ES:", False, "Futures with empty exchange"),
            ("/:XCME", False, "Futures with empty symbol"),
            ("/ES:XC@ME", False, "Futures with invalid exchange chars"),
            ("ES:XCME", False, "Futures without leading slash"),
            ("/TOOLONGSYMBOL:XCME", False, "Futures symbol too long"),
            ("/ES:TOOLONGEXCHANGE", False, "Exchange name too long"),
            ("TOOLONGEQUITYSYMBOL", False, "Equity symbol too long"),
            ("123", False, "Numbers only"),
            ("SP X", False, "Space in symbol"),
            ("/ES :XCME", False, "Space in futures symbol"),
            ("/ES: XCME", False, "Space in exchange"),
        ]
        
        passed = 0
        failed = 0
        
        for symbol, should_pass, description in test_cases:
            try:
                state = ControlPanelStateV2_5(
                    symbol=symbol,
                    dte_min=0,
                    dte_max=5,
                    price_range_percent=10,
                    refresh_interval_seconds=30
                )
                
                if should_pass:
                    logger.info(f"✅ PASS: '{symbol}' - {description}")
                    logger.info(f"   Normalized to: '{state.symbol}'")
                    passed += 1
                else:
                    logger.error(f"❌ FAIL: '{symbol}' should have been rejected but was accepted - {description}")
                    failed += 1
                    
            except Exception as e:
                if not should_pass:
                    logger.info(f"✅ PASS: '{symbol}' correctly rejected - {description}")
                    logger.info(f"   Error: {str(e)}")
                    passed += 1
                else:
                    logger.error(f"❌ FAIL: '{symbol}' should have been accepted but was rejected - {description}")
                    logger.error(f"   Error: {str(e)}")
                    failed += 1
        
        logger.info(f"📊 ControlPanelStateV2_5 Results: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        logger.error(f"❌ Test setup error: {e}", exc_info=True)
        return False

def test_control_panel_parameters_futures_symbols():
    """Test ControlPanelParametersV2_5 with futures symbols"""
    logger.info("🧪 TESTING: ControlPanelParametersV2_5 Futures Symbol Support")
    
    try:
        from data_models.core_system_config import ControlPanelParametersV2_5
        
        # Test key futures symbols
        futures_symbols = ["/ES:XCME", "/NQ:XCME", "/YM:XCME", "/RTY:XCME", "/CL:NYMEX", "/GC:COMEX"]
        
        passed = 0
        failed = 0
        
        for symbol in futures_symbols:
            try:
                params = ControlPanelParametersV2_5(
                    symbol=symbol,
                    dte_min=0,
                    dte_max=5,
                    price_range_percent=10,
                    fetch_interval_seconds=30
                )
                
                logger.info(f"✅ PASS: '{symbol}' accepted in ControlPanelParametersV2_5")
                logger.info(f"   Normalized to: '{params.symbol}'")
                passed += 1
                
            except Exception as e:
                logger.error(f"❌ FAIL: '{symbol}' rejected in ControlPanelParametersV2_5")
                logger.error(f"   Error: {str(e)}")
                failed += 1
        
        logger.info(f"📊 ControlPanelParametersV2_5 Results: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        logger.error(f"❌ Test setup error: {e}", exc_info=True)
        return False

def test_symbol_normalization():
    """Test that symbol normalization works correctly"""
    logger.info("🧪 TESTING: Symbol Normalization")
    
    try:
        from data_models.core_system_config import ControlPanelStateV2_5
        
        # Test normalization cases: (input, expected_output)
        normalization_cases = [
            ("spx", "SPX"),
            ("  spy  ", "SPY"),
            ("/es:xcme", "/ES:XCME"),
            ("  /nq:xcme  ", "/NQ:XCME"),
            ("/ES:xcme", "/ES:XCME"),
            ("/es:XCME", "/ES:XCME"),
        ]
        
        passed = 0
        failed = 0
        
        for input_symbol, expected_output in normalization_cases:
            try:
                state = ControlPanelStateV2_5(
                    symbol=input_symbol,
                    dte_min=0,
                    dte_max=5,
                    price_range_percent=10,
                    refresh_interval_seconds=30
                )
                
                if state.symbol == expected_output:
                    logger.info(f"✅ PASS: '{input_symbol}' normalized to '{state.symbol}' (expected '{expected_output}')")
                    passed += 1
                else:
                    logger.error(f"❌ FAIL: '{input_symbol}' normalized to '{state.symbol}' but expected '{expected_output}'")
                    failed += 1
                    
            except Exception as e:
                logger.error(f"❌ FAIL: '{input_symbol}' caused error during normalization")
                logger.error(f"   Error: {str(e)}")
                failed += 1
        
        logger.info(f"📊 Symbol Normalization Results: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        logger.error(f"❌ Test setup error: {e}", exc_info=True)
        return False

def test_backwards_compatibility():
    """Test that existing equity symbols still work"""
    logger.info("🧪 TESTING: Backwards Compatibility with Equity Symbols")
    
    try:
        from data_models.core_system_config import ControlPanelStateV2_5
        
        # Common equity symbols that should continue to work
        equity_symbols = ["SPX", "SPY", "QQQ", "IWM", "DIA", "VIX", "AAPL", "TSLA", "MSFT", "GOOGL"]
        
        passed = 0
        failed = 0
        
        for symbol in equity_symbols:
            try:
                state = ControlPanelStateV2_5(
                    symbol=symbol,
                    dte_min=0,
                    dte_max=5,
                    price_range_percent=10,
                    refresh_interval_seconds=30
                )
                
                logger.info(f"✅ PASS: Equity symbol '{symbol}' still works")
                passed += 1
                
            except Exception as e:
                logger.error(f"❌ FAIL: Equity symbol '{symbol}' no longer works")
                logger.error(f"   Error: {str(e)}")
                failed += 1
        
        logger.info(f"📊 Backwards Compatibility Results: {passed} passed, {failed} failed")
        return failed == 0
        
    except Exception as e:
        logger.error(f"❌ Test setup error: {e}", exc_info=True)
        return False

def main():
    """Run all futures symbol validation tests"""
    logger.info("🚀 FUTURES SYMBOL VALIDATION TEST SUITE")
    logger.info("=" * 70)
    
    # Test 1: ControlPanelStateV2_5 futures support
    state_success = test_control_panel_state_futures_symbols()
    
    logger.info("=" * 40)
    
    # Test 2: ControlPanelParametersV2_5 futures support
    params_success = test_control_panel_parameters_futures_symbols()
    
    logger.info("=" * 40)
    
    # Test 3: Symbol normalization
    normalization_success = test_symbol_normalization()
    
    logger.info("=" * 40)
    
    # Test 4: Backwards compatibility
    compatibility_success = test_backwards_compatibility()
    
    # Summary
    logger.info("=" * 70)
    logger.info("📊 FUTURES SYMBOL VALIDATION RESULTS:")
    logger.info(f"✅ ControlPanelStateV2_5: {'PASS' if state_success else 'FAIL'}")
    logger.info(f"✅ ControlPanelParametersV2_5: {'PASS' if params_success else 'FAIL'}")
    logger.info(f"✅ Symbol Normalization: {'PASS' if normalization_success else 'FAIL'}")
    logger.info(f"✅ Backwards Compatibility: {'PASS' if compatibility_success else 'FAIL'}")
    
    all_passed = state_success and params_success and normalization_success and compatibility_success
    
    if all_passed:
        logger.info("🎉 ALL TESTS PASSED - Futures symbol support implemented successfully!")
        logger.info("📋 Key improvements:")
        logger.info("   • Futures symbols (/ES:XCME, /NQ:XCME, etc.) now supported")
        logger.info("   • Equity symbols (SPX, SPY, etc.) continue to work")
        logger.info("   • Proper symbol normalization (uppercase, trimmed)")
        logger.info("   • Strict validation prevents malformed symbols")
        logger.info("   • Zero tolerance for invalid symbols that could cause pipeline failures")
        logger.info("   • ConvexValue futures data integration ready")
    else:
        logger.error("💥 SOME TESTS FAILED - Additional fixes may be needed")
    
    return all_passed

if __name__ == "__main__":
    main()
