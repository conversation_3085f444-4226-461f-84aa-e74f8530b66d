#!/usr/bin/env python3
"""
Quick Dashboard Import Test
===========================

Test the core dashboard imports to verify our Pydantic v2 fixes work.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """Test basic imports work."""
    print("🔧 Testing basic imports...")
    
    try:
        # Test configuration manager
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        print("✅ ConfigManagerV2_5 import successful")
        
        # Test data models
        from data_models import FinalAnalysisBundleV2_5, DashboardModeSettings
        print("✅ Data models import successful")
        
        # Test dashboard application
        from dashboard_application.app_main import create_dash_app
        print("✅ Dashboard app import successful")
        
        return True
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_config_loading():
    """Test configuration loading with Pydantic v2."""
    print("\n🔧 Testing configuration loading...")
    
    try:
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        # Initialize configuration
        config_manager = ConfigManagerV2_5()
        print("✅ ConfigManagerV2_5 initialized")
        
        # Test Pydantic v2 model access
        config = config_manager.config
        if hasattr(config, 'model_dump'):
            print("✅ Config is a Pydantic v2 model")
        else:
            print("❌ Config is not a Pydantic v2 model")
            return False
        
        # Test dashboard config access
        dashboard_config = config.visualization_settings.dashboard
        if hasattr(dashboard_config, 'model_dump'):
            print("✅ Dashboard config is a Pydantic v2 model")
        else:
            print("❌ Dashboard config is not a Pydantic v2 model")
            return False
        
        # Test modes config access
        modes_config = dashboard_config.modes_detail_config
        if hasattr(modes_config, 'model_dump'):
            print("✅ Modes config is a Pydantic v2 model")
        else:
            print("❌ Modes config is not a Pydantic v2 model")
            return False
        
        # Test specific mode access
        if hasattr(modes_config, 'ai'):
            ai_mode = modes_config.ai
            print(f"✅ AI mode found: {ai_mode.label}")
            print(f"   Module: {ai_mode.module_name}")
        else:
            print("❌ AI mode not found")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mode_imports():
    """Test dashboard mode imports."""
    print("\n🔧 Testing dashboard mode imports...")
    
    modes = {
        'main': 'dashboard_application.modes.main_dashboard_display_v2_5',
        'ai': 'dashboard_application.modes.ai_dashboard.ai_dashboard_display_v2_5'
    }
    
    success_count = 0
    
    for mode_name, module_path in modes.items():
        try:
            import importlib
            module = importlib.import_module(module_path)
            
            if hasattr(module, 'create_layout'):
                print(f"✅ {mode_name} mode import successful")
                success_count += 1
            else:
                print(f"❌ {mode_name} mode missing create_layout function")
        except Exception as e:
            print(f"❌ {mode_name} mode import failed: {e}")
    
    return success_count == len(modes)

def main():
    """Run all tests."""
    print("🚀 EOTS v2.5 Dashboard Import Tests")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_config_loading,
        test_mode_imports
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n🏁 Results: {passed}/{len(tests)} tests passed")
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
