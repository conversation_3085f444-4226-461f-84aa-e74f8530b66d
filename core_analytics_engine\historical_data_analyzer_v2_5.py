"""
Historical Data Analyzer v2.5
Provides proper historical analysis for stale/weekend data without fake calculations.
Maintains zero tolerance fake data policy while enabling meaningful weekend analysis.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from pydantic import BaseModel

from .data_freshness_manager_v2_5 import DataFreshnessInfo, DataFreshnessTier

logger = logging.getLogger(__name__)

class HistoricalAnalysisResult(BaseModel):
    """Result of historical data analysis"""
    elite_impact_score_und: float
    institutional_flow_score_und: float
    flow_momentum_index_und: float
    market_regime_elite: str
    flow_type_elite: str
    volatility_regime_elite: str
    analysis_confidence: str
    data_source_label: str
    calculation_method: str

class HistoricalDataAnalyzerV2_5:
    """
    Analyzes historical/stale data without creating fake calculations.
    Uses real market data with transparent labeling about data age and limitations.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("HistoricalDataAnalyzerV2_5 initialized")
    
    def analyze_historical_data(
        self, 
        raw_underlying_data: Any, 
        freshness_info: DataFreshnessInfo,
        foundational_metrics: Dict[str, float]
    ) -> HistoricalAnalysisResult:
        """
        Analyze historical data using real market data without fake calculations.
        
        Args:
            raw_underlying_data: Raw underlying data model
            freshness_info: Data freshness classification
            foundational_metrics: Real calculated foundational metrics
            
        Returns:
            HistoricalAnalysisResult: Historical analysis with transparency labels
        """
        self.logger.info(f"Analyzing historical data - {freshness_info.analysis_mode}")
        
        if freshness_info.tier == DataFreshnessTier.TIER_2_RECENT_STALE:
            return self._analyze_recent_stale_data(raw_underlying_data, freshness_info, foundational_metrics)
        elif freshness_info.tier == DataFreshnessTier.TIER_3_WEEKEND_HOLIDAY:
            return self._analyze_weekend_data(raw_underlying_data, freshness_info, foundational_metrics)
        else:
            raise ValueError(f"Invalid tier for historical analysis: {freshness_info.tier}")
    
    def _analyze_recent_stale_data(
        self, 
        raw_underlying_data: Any, 
        freshness_info: DataFreshnessInfo,
        foundational_metrics: Dict[str, float]
    ) -> HistoricalAnalysisResult:
        """Analyze recent stale data (1-24 hours old)"""
        
        # Use real price data if available
        current_price = getattr(raw_underlying_data, 'price', 0.0)
        prev_close = getattr(raw_underlying_data, 'prev_day_close_price_und', None)
        
        # Calculate real price-based metrics
        if prev_close and prev_close > 0:
            price_change_pct = abs((current_price - prev_close) / prev_close)
            price_direction = "up" if current_price > prev_close else "down"
        else:
            # Use available price data without assumptions
            price_change_pct = 0.0
            price_direction = "neutral"
        
        # Use real gamma imbalance data
        call_gxoi = getattr(raw_underlying_data, 'call_gxoi', 0.0) or 0.0
        put_gxoi = getattr(raw_underlying_data, 'put_gxoi', 0.0) or 0.0
        total_gxoi = abs(call_gxoi) + abs(put_gxoi)
        
        # Calculate historical elite metrics based on real data
        elite_impact_score = self._calculate_historical_elite_impact(
            price_change_pct, total_gxoi, foundational_metrics
        )
        
        institutional_flow_score = self._calculate_historical_institutional_flow(
            call_gxoi, put_gxoi, foundational_metrics
        )
        
        flow_momentum_index = self._calculate_historical_flow_momentum(
            price_change_pct, price_direction, foundational_metrics
        )
        
        # Determine market regime based on real market conditions
        market_regime, volatility_regime = self._classify_historical_regime(
            price_change_pct, total_gxoi
        )
        
        flow_type = self._classify_historical_flow_type(call_gxoi, put_gxoi)
        
        return HistoricalAnalysisResult(
            elite_impact_score_und=elite_impact_score,
            institutional_flow_score_und=institutional_flow_score,
            flow_momentum_index_und=flow_momentum_index,
            market_regime_elite=market_regime,
            flow_type_elite=flow_type,
            volatility_regime_elite=volatility_regime,
            analysis_confidence="MODERATE_STALE_DATA",
            data_source_label=f"Historical Analysis ({freshness_info.data_age_hours:.1f}h old)",
            calculation_method="REAL_HISTORICAL_DATA_ANALYSIS"
        )
    
    def _analyze_weekend_data(
        self, 
        raw_underlying_data: Any, 
        freshness_info: DataFreshnessInfo,
        foundational_metrics: Dict[str, float]
    ) -> HistoricalAnalysisResult:
        """Analyze weekend/holiday data (>24 hours old)"""
        
        # Use last available real market data
        current_price = getattr(raw_underlying_data, 'price', 0.0)
        
        # Use real gamma exposure data from last trading session
        call_gxoi = getattr(raw_underlying_data, 'call_gxoi', 0.0) or 0.0
        put_gxoi = getattr(raw_underlying_data, 'put_gxoi', 0.0) or 0.0
        total_gxoi = abs(call_gxoi) + abs(put_gxoi)
        
        # Weekend analysis based on last trading session data
        elite_impact_score = self._calculate_weekend_elite_impact(
            total_gxoi, foundational_metrics
        )
        
        institutional_flow_score = self._calculate_weekend_institutional_flow(
            call_gxoi, put_gxoi, foundational_metrics
        )
        
        # Weekend flow momentum is neutral (no intraday movement)
        flow_momentum_index = 0.0
        
        # Weekend regime classification
        market_regime, volatility_regime = self._classify_weekend_regime(total_gxoi)
        flow_type = self._classify_historical_flow_type(call_gxoi, put_gxoi)
        
        return HistoricalAnalysisResult(
            elite_impact_score_und=elite_impact_score,
            institutional_flow_score_und=institutional_flow_score,
            flow_momentum_index_und=flow_momentum_index,
            market_regime_elite=market_regime,
            flow_type_elite=flow_type,
            volatility_regime_elite=volatility_regime,
            analysis_confidence="LOW_WEEKEND_DATA",
            data_source_label=f"Weekend Analysis ({freshness_info.data_timestamp.strftime('%A %b %d')})",
            calculation_method="WEEKEND_HISTORICAL_MODE"
        )
    
    def _calculate_historical_elite_impact(
        self, price_change_pct: float, total_gxoi: float, foundational_metrics: Dict[str, float]
    ) -> float:
        """Calculate elite impact based on real historical data"""
        gib_magnitude = abs(foundational_metrics.get('gib_oi_based_und', 0.0))
        
        # Base score from real gamma imbalance
        base_score = min(50.0, (gib_magnitude / 10000.0))
        
        # Adjust for real price movement
        price_adjustment = price_change_pct * 100
        
        # Adjust for real gamma exposure
        gxoi_adjustment = min(20.0, total_gxoi / 1000.0)
        
        return min(100.0, max(1.0, base_score + price_adjustment + gxoi_adjustment))
    
    def _calculate_historical_institutional_flow(
        self, call_gxoi: float, put_gxoi: float, foundational_metrics: Dict[str, float]
    ) -> float:
        """Calculate institutional flow based on real gamma data"""
        total_gxoi = abs(call_gxoi) + abs(put_gxoi)
        
        # Base institutional activity from real gamma exposure
        base_score = min(60.0, (total_gxoi / 500.0))
        
        # Imbalance factor
        if total_gxoi > 0:
            imbalance = abs(call_gxoi - put_gxoi) / total_gxoi
            imbalance_adjustment = imbalance * 20.0
        else:
            imbalance_adjustment = 0.0
        
        return min(100.0, max(1.0, base_score + imbalance_adjustment + 20.0))
    
    def _calculate_historical_flow_momentum(
        self, price_change_pct: float, price_direction: str, foundational_metrics: Dict[str, float]
    ) -> float:
        """Calculate flow momentum based on real price movement"""
        if price_direction == "up":
            return min(50.0, price_change_pct * 1000)
        elif price_direction == "down":
            return max(-50.0, -price_change_pct * 1000)
        else:
            return 0.0
    
    def _calculate_weekend_elite_impact(
        self, total_gxoi: float, foundational_metrics: Dict[str, float]
    ) -> float:
        """Calculate weekend elite impact from gamma positioning"""
        gib_magnitude = abs(foundational_metrics.get('gib_oi_based_und', 0.0))
        base_score = min(40.0, (gib_magnitude / 15000.0))
        gxoi_adjustment = min(15.0, total_gxoi / 1000.0)
        return min(100.0, max(1.0, base_score + gxoi_adjustment + 15.0))
    
    def _calculate_weekend_institutional_flow(
        self, call_gxoi: float, put_gxoi: float, foundational_metrics: Dict[str, float]
    ) -> float:
        """Calculate weekend institutional flow from positioning"""
        total_gxoi = abs(call_gxoi) + abs(put_gxoi)
        return min(100.0, max(1.0, (total_gxoi / 800.0) + 25.0))
    
    def _classify_historical_regime(
        self, price_change_pct: float, total_gxoi: float
    ) -> Tuple[str, str]:
        """Classify market regime based on real historical data"""
        if price_change_pct > 0.025:  # > 2.5% move
            return 'high_volatility', 'elevated'
        elif price_change_pct > 0.015:  # > 1.5% move
            return 'moderate_volatility', 'moderate'
        elif total_gxoi > 5000:  # High gamma exposure
            return 'low_volatility_high_gamma', 'compressed'
        else:
            return 'low_volatility', 'subdued'
    
    def _classify_weekend_regime(self, total_gxoi: float) -> Tuple[str, str]:
        """Classify weekend regime based on positioning"""
        if total_gxoi > 8000:
            return 'weekend_high_gamma', 'positioned'
        elif total_gxoi > 3000:
            return 'weekend_moderate_gamma', 'neutral'
        else:
            return 'weekend_low_gamma', 'quiet'
    
    def _classify_historical_flow_type(self, call_gxoi: float, put_gxoi: float) -> str:
        """Classify flow type based on real gamma imbalance"""
        if abs(call_gxoi) > abs(put_gxoi) * 1.5:
            return 'call_heavy'
        elif abs(put_gxoi) > abs(call_gxoi) * 1.5:
            return 'put_heavy'
        else:
            return 'balanced'
