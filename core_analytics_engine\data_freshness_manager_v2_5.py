"""
Data Freshness Manager v2.5
Implements tiered data freshness detection and handling for EOTS v2.5 system.
Eliminates fake data calculations while maintaining analytical capabilities during non-market hours.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple, Literal
from enum import Enum
import pytz
from pydantic import BaseModel, Field

# Configure logging
logger = logging.getLogger(__name__)

class DataFreshnessTier(Enum):
    """Data freshness classification tiers"""
    TIER_1_FRESH = "TIER_1_FRESH"           # < 1 hour old during market hours
    TIER_2_RECENT_STALE = "TIER_2_RECENT_STALE"  # 1-24 hours old
    TIER_3_WEEKEND_HOLIDAY = "TIER_3_WEEKEND_HOLIDAY"  # > 24 hours old

class DataFreshnessInfo(BaseModel):
    """Data freshness information model"""
    tier: DataFreshnessTier = Field(..., description="Data freshness tier classification")
    data_age_hours: float = Field(..., description="Age of data in hours")
    data_timestamp: datetime = Field(..., description="Original data timestamp")
    current_timestamp: datetime = Field(..., description="Current analysis timestamp")
    is_market_hours: bool = Field(..., description="Whether current time is during market hours")
    is_trading_day: bool = Field(..., description="Whether current day is a trading day")
    freshness_label: str = Field(..., description="Human-readable freshness description")
    analysis_mode: str = Field(..., description="Recommended analysis mode")

class DataFreshnessManagerV2_5:
    """
    Manages data freshness detection and classification for EOTS v2.5 system.
    Implements zero tolerance fake data policy with proper historical data handling.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Market hours configuration (Eastern Time)
        self.market_timezone = pytz.timezone('US/Eastern')
        self.market_open_time = "09:30"
        self.market_close_time = "16:00"
        
        # Freshness thresholds
        self.tier_1_threshold_hours = 1.0    # Fresh data threshold
        self.tier_2_threshold_hours = 24.0   # Recent stale threshold
        
        self.logger.info("DataFreshnessManagerV2_5 initialized with tiered freshness detection")
    
    def classify_data_freshness(self, data_timestamp: datetime) -> DataFreshnessInfo:
        """
        Classify data freshness based on timestamp and market conditions.
        
        Args:
            data_timestamp: Timestamp of the data to classify
            
        Returns:
            DataFreshnessInfo: Complete freshness classification and metadata
        """
        current_time = datetime.now(self.market_timezone)
        
        # Ensure data_timestamp is timezone-aware
        if data_timestamp.tzinfo is None:
            data_timestamp = self.market_timezone.localize(data_timestamp)
        else:
            data_timestamp = data_timestamp.astimezone(self.market_timezone)
        
        # Calculate data age
        time_diff = current_time - data_timestamp
        age_hours = time_diff.total_seconds() / 3600
        
        # Determine market conditions
        is_market_hours = self._is_market_hours(current_time)
        is_trading_day = self._is_trading_day(current_time)
        
        # Classify freshness tier
        if age_hours < self.tier_1_threshold_hours and is_market_hours and is_trading_day:
            tier = DataFreshnessTier.TIER_1_FRESH
            freshness_label = f"LIVE DATA ({age_hours:.1f}h old)"
            analysis_mode = "REAL_TIME_ELITE_INTELLIGENCE"
            
        elif age_hours < self.tier_2_threshold_hours:
            tier = DataFreshnessTier.TIER_2_RECENT_STALE
            freshness_label = f"RECENT DATA ({age_hours:.1f}h old)"
            analysis_mode = "HISTORICAL_ELITE_ANALYSIS"
            
        else:
            tier = DataFreshnessTier.TIER_3_WEEKEND_HOLIDAY
            freshness_label = f"WEEKEND/HOLIDAY DATA ({age_hours:.1f}h old)"
            analysis_mode = "WEEKEND_HISTORICAL_MODE"
        
        freshness_info = DataFreshnessInfo(
            tier=tier,
            data_age_hours=age_hours,
            data_timestamp=data_timestamp,
            current_timestamp=current_time,
            is_market_hours=is_market_hours,
            is_trading_day=is_trading_day,
            freshness_label=freshness_label,
            analysis_mode=analysis_mode
        )
        
        self.logger.debug(f"Data freshness classified: {tier.value} - {freshness_label}")
        return freshness_info
    
    def _is_market_hours(self, timestamp: datetime) -> bool:
        """Check if timestamp falls within market hours"""
        time_str = timestamp.strftime("%H:%M")
        return self.market_open_time <= time_str <= self.market_close_time
    
    def _is_trading_day(self, timestamp: datetime) -> bool:
        """Check if timestamp falls on a trading day (Monday-Friday)"""
        return timestamp.weekday() < 5  # 0-4 are Monday-Friday
    
    def get_freshness_display_info(self, freshness_info: DataFreshnessInfo) -> Dict[str, Any]:
        """
        Generate display information for dashboard transparency.
        
        Args:
            freshness_info: Data freshness classification
            
        Returns:
            Dict containing display styling and labels
        """
        if freshness_info.tier == DataFreshnessTier.TIER_1_FRESH:
            return {
                "status_color": "#00FF00",  # Green
                "status_icon": "🟢",
                "status_text": "LIVE",
                "timestamp_display": freshness_info.data_timestamp.strftime("%H:%M:%S EST"),
                "warning_message": None,
                "css_class": "data-fresh"
            }
            
        elif freshness_info.tier == DataFreshnessTier.TIER_2_RECENT_STALE:
            return {
                "status_color": "#FFA500",  # Orange
                "status_icon": "🟡",
                "status_text": "STALE",
                "timestamp_display": freshness_info.data_timestamp.strftime("%a %H:%M EST"),
                "warning_message": f"Data is {freshness_info.data_age_hours:.1f} hours old",
                "css_class": "data-stale"
            }
            
        else:  # TIER_3_WEEKEND_HOLIDAY
            return {
                "status_color": "#808080",  # Gray
                "status_icon": "📅",
                "status_text": "WEEKEND",
                "timestamp_display": freshness_info.data_timestamp.strftime("%a %b %d, %H:%M EST"),
                "warning_message": f"Weekend/Holiday data from {freshness_info.data_timestamp.strftime('%A')}",
                "css_class": "data-weekend"
            }
    
    def should_use_elite_intelligence(self, freshness_info: DataFreshnessInfo) -> bool:
        """
        Determine if full elite intelligence calculations should be used.
        
        Args:
            freshness_info: Data freshness classification
            
        Returns:
            bool: True if elite intelligence should be used
        """
        return freshness_info.tier == DataFreshnessTier.TIER_1_FRESH
    
    def get_analysis_strategy(self, freshness_info: DataFreshnessInfo) -> str:
        """
        Get the recommended analysis strategy based on data freshness.
        
        Args:
            freshness_info: Data freshness classification
            
        Returns:
            str: Analysis strategy identifier
        """
        strategy_map = {
            DataFreshnessTier.TIER_1_FRESH: "FULL_ELITE_INTELLIGENCE",
            DataFreshnessTier.TIER_2_RECENT_STALE: "HISTORICAL_ANALYSIS_WITH_LABELS",
            DataFreshnessTier.TIER_3_WEEKEND_HOLIDAY: "WEEKEND_HISTORICAL_MODE"
        }
        
        return strategy_map[freshness_info.tier]
