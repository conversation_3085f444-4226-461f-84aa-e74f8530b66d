#!/usr/bin/env python3
"""
Test script to check what OHLCV data we can get from Tradier
and calculate price changes for SPX
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import EOTS components
from utils.config_manager_v2_5 import ConfigManagerV2_5
from data_management.tradier_data_fetcher_v2_5 import TradierDataFetcherV2_5

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(name)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

async def test_tradier_ohlcv():
    """Test Tradier OHLCV data fetching and price change calculations"""
    
    try:
        # Initialize config
        logger.info("🔧 Loading configuration...")
        config = ConfigManagerV2_5()
        
        # Test symbol
        symbol = "SPX"
        
        logger.info(f"🔍 Testing Tradier OHLCV data fetch for {symbol}...")
        
        # Initialize Tradier fetcher
        async with TradierDataFetcherV2_5(config) as tradier:
            
            # Test 1: Get current quote
            logger.info("📊 Test 1: Fetching current underlying quote...")
            current_quote = await tradier.fetch_underlying_quote(symbol)
            if current_quote:
                logger.info(f"✅ Current quote data:")
                logger.info(f"   Symbol: {current_quote.symbol}")
                logger.info(f"   Price: ${current_quote.price}")
                logger.info(f"   Volume: {current_quote.day_volume}")
                logger.info(f"   Volatility: {current_quote.u_volatility}")
                
                # Check if Tradier already provides price change fields
                logger.info(f"   Price Change Abs: {current_quote.price_change_abs_und}")
                logger.info(f"   Price Change Pct: {current_quote.price_change_pct_und}")
                logger.info(f"   Day Open: {current_quote.day_open_price_und}")
                logger.info(f"   Day High: {current_quote.day_high_price_und}")
                logger.info(f"   Day Low: {current_quote.day_low_price_und}")
                logger.info(f"   Prev Close: {current_quote.prev_day_close_price_und}")
            else:
                logger.error("❌ Failed to get current quote from Tradier")
                
            # Test 2: Get historical OHLCV data
            logger.info("\n📈 Test 2: Fetching historical OHLCV data...")
            historical_data = await tradier.fetch_historical_data(symbol, days=3)
            if historical_data and 'data' in historical_data:
                logger.info(f"✅ Historical data retrieved:")
                logger.info(f"   Symbol: {historical_data['symbol']}")
                logger.info(f"   Days of data: {len(historical_data['data'])}")
                
                # Show the data
                for i, day_data in enumerate(historical_data['data']):
                    logger.info(f"   Day {i+1}: {day_data}")
                
                # Test price change calculation
                if len(historical_data['data']) >= 2:
                    logger.info("\n🧮 Test 3: Calculating price changes...")
                    
                    # Get current price (from quote or latest close)
                    current_price = float(current_quote.price) if current_quote else None
                    
                    # Get previous close
                    latest_day = historical_data['data'][-1]
                    prev_day = historical_data['data'][-2]
                    
                    day_open = float(latest_day.get('open', 0))
                    day_high = float(latest_day.get('high', 0))
                    day_low = float(latest_day.get('low', 0))
                    day_close = float(latest_day.get('close', 0))
                    prev_close = float(prev_day.get('close', 0))
                    
                    logger.info(f"   📊 OHLCV Data:")
                    logger.info(f"      Today's Open: ${day_open:.2f}")
                    logger.info(f"      Today's High: ${day_high:.2f}")
                    logger.info(f"      Today's Low: ${day_low:.2f}")
                    logger.info(f"      Today's Close: ${day_close:.2f}")
                    logger.info(f"      Previous Close: ${prev_close:.2f}")
                    
                    if current_price:
                        logger.info(f"      Current Price: ${current_price:.2f}")
                        
                        # Calculate price changes using current price vs previous close
                        if prev_close > 0:
                            price_change_abs = current_price - prev_close
                            price_change_pct = (price_change_abs / prev_close)
                            
                            logger.info(f"\n💰 CALCULATED PRICE CHANGES:")
                            logger.info(f"   price_change_abs_und = ${price_change_abs:.2f}")
                            logger.info(f"   price_change_pct_und = {price_change_pct:.6f} ({price_change_pct*100:.2f}%)")
                            
                            # Show what we would populate in the model
                            logger.info(f"\n🎯 FIELDS TO POPULATE IN UNDERLYING DATA:")
                            logger.info(f"   price_change_abs_und: {price_change_abs}")
                            logger.info(f"   price_change_pct_und: {price_change_pct}")
                            logger.info(f"   day_open_price_und: {day_open}")
                            logger.info(f"   day_high_price_und: {day_high}")
                            logger.info(f"   day_low_price_und: {day_low}")
                            logger.info(f"   prev_day_close_price_und: {prev_close}")
                        else:
                            logger.warning("⚠️ Previous close is 0 - cannot calculate percentage change")
                    else:
                        logger.warning("⚠️ No current price available")
                else:
                    logger.warning("⚠️ Need at least 2 days of historical data for price change calculation")
            else:
                logger.error("❌ Failed to get historical data from Tradier")
                
    except Exception as e:
        logger.error(f"❌ Test failed: {e}", exc_info=True)

if __name__ == "__main__":
    logger.info("🚀 Starting Tradier OHLCV Test...")
    asyncio.run(test_tradier_ohlcv())
    logger.info("✅ Test completed!")
