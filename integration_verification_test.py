#!/usr/bin/env python3
"""
EOTS v2.5 Integration Verification Test
Tests the critical fixes applied to the system.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

def test_elite_impact_columns():
    """Test that EliteImpactColumns has the required attributes."""
    print("\n1. Testing EliteImpactColumns Attributes...")
    try:
        from core_analytics_engine.eots_metrics.elite_intelligence import EliteImpactColumns
        print("   ✅ EliteImpactColumns imported successfully")
        
        # Check for the fixed attributes
        attrs_to_check = [
            'SDAG_CONSENSUS', 
            'PREDICTION_CONFIDENCE', 
            'SIGNAL_STRENGTH', 
            'STRIKE_MAGNETISM_INDEX', 
            'VOLATILITY_PRESSURE_INDEX'
        ]
        
        for attr in attrs_to_check:
            if hasattr(EliteImpactColumns, attr):
                value = getattr(EliteImpactColumns, attr)
                print(f"   ✅ {attr}: '{value}'")
            else:
                print(f"   ❌ {attr}: MISSING")
                return False
                
        return True
        
    except Exception as e:
        print(f"   ❌ Import failed: {e}")
        return False

def test_pydantic_models():
    """Test Pydantic v2 model functionality."""
    print("\n2. Testing Pydantic v2 Models...")
    try:
        from data_models import ProcessedStrikeLevelMetricsV2_5
        from datetime import datetime
        
        # Test model creation with SDAG fields
        strike_model = ProcessedStrikeLevelMetricsV2_5(
            strike=100.0,
            symbol='TEST',
            timestamp=datetime.now(),
            underlying_price=100.0,
            sdag_consensus=0.5,
            prediction_confidence=0.7,
            signal_strength=0.8,
            strike_magnetism_index=1.2,
            volatility_pressure_index=0.9
        )
        
        print("   ✅ ProcessedStrikeLevelMetricsV2_5 created with SDAG fields")
        print(f"   ✅ SDAG Consensus: {strike_model.sdag_consensus}")
        print(f"   ✅ Prediction Confidence: {strike_model.prediction_confidence}")
        print(f"   ✅ Signal Strength: {strike_model.signal_strength}")
        print(f"   ✅ Strike Magnetism Index: {strike_model.strike_magnetism_index}")
        print(f"   ✅ Volatility Pressure Index: {strike_model.volatility_pressure_index}")
        
        # Test model_dump (Pydantic v2 syntax)
        model_dict = strike_model.model_dump()
        print(f"   ✅ model_dump() works: {len(model_dict)} fields")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Pydantic model test failed: {e}")
        return False

def test_configuration_loading():
    """Test configuration loading with Pydantic v2."""
    print("\n3. Testing Configuration Loading...")
    try:
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        config_manager = ConfigManagerV2_5()
        print("   ✅ ConfigManagerV2_5 loaded successfully")
        print(f"   ✅ Config type: {type(config_manager.config)}")
        print(f"   ✅ Has model_dump: {hasattr(config_manager.config, 'model_dump')}")
        
        # Test config access
        if hasattr(config_manager.config, 'model_dump'):
            config_dict = config_manager.config.model_dump()
            print(f"   ✅ Config has {len(config_dict)} top-level settings")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False

def test_metrics_calculator():
    """Test that MetricsCalculatorV2_5 has the new elite intelligence integration."""
    print("\n4. Testing Metrics Calculator Integration...")
    try:
        from core_analytics_engine.eots_metrics import MetricsCalculatorV2_5
        from utils.config_manager_v2_5 import ConfigManagerV2_5
        
        config_manager = ConfigManagerV2_5()
        
        # Create metrics calculator (this tests the elite intelligence integration)
        metrics_calc = MetricsCalculatorV2_5(
            config_manager=config_manager,
            historical_data_manager=None,
            enhanced_cache_manager=None
        )
        
        print("   ✅ MetricsCalculatorV2_5 created successfully")
        print(f"   ✅ Has elite_intelligence: {hasattr(metrics_calc, 'elite_intelligence')}")
        print(f"   ✅ Has _calculate_strike_level_elite_metrics: {hasattr(metrics_calc, '_calculate_strike_level_elite_metrics')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Metrics calculator test failed: {e}")
        return False

def main():
    """Run all integration verification tests."""
    print("🧪 EOTS v2.5 Integration Verification Test")
    print("=" * 50)
    
    tests = [
        test_elite_impact_columns,
        test_pydantic_models,
        test_configuration_loading,
        test_metrics_calculator
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n🎯 Integration Verification Results")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🟢 ALL TESTS PASSED - System is ready for operation!")
    else:
        print("🟡 Some tests failed - Review issues above")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
