"""
Pydantic models for top-level data bundles used in EOTS v2.5,
primarily for packaging comprehensive analysis results for consumption
by the dashboard or other system outputs.
"""
from pydantic import BaseModel, Field, ConfigDict
from typing import List, Dict, Optional, Any, TYPE_CHECKING
from datetime import datetime

# Import necessary schemas from other new modules
from .processed_data import ProcessedDataBundleV2_5
from .signal_level_schemas import SignalPayloadV2_5, KeyLevelsDataV2_5
from .atif_schemas import ATIFStrategyDirectivePayloadV2_5 # For ATIF's pre-TPO directives
from .recommendation_schemas import ActiveRecommendationPayloadV2_5

# Import dependencies or use TYPE_CHECKING for forward references
from .raw_data import RawOptionsContractV2_5


class UnprocessedDataBundleV2_5(BaseModel):
    """Container for all raw/unprocessed market data before analytics and metrics are applied."""
    options_contracts: List[RawOptionsContractV2_5] = Field(default_factory=list, description="Raw options contracts fetched from data provider.")
    underlying_data: Any = Field(..., description="Raw underlying asset data.")
    fetch_timestamp: datetime = Field(..., description="Timestamp when data was fetched.")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered during data fetch.")
    model_config = ConfigDict(extra='forbid')


class FinalAnalysisBundleV2_5(BaseModel):
    """
    The comprehensive, top-level data structure that encapsulates all analytical
    outputs for a single symbol from one full EOTS v2.5 analysis cycle.
    This bundle is the primary data product consumed by the dashboard for
    visualization and by any external systems that need the complete analytical picture.
    """
    processed_data_bundle: ProcessedDataBundleV2_5 = Field(..., description="Contains all metric-enriched data: options, strike-level, and underlying aggregates (which includes regime and ticker context).")
    scored_signals_v2_5: Dict[str, List[SignalPayloadV2_5]] = Field(default_factory=dict, description="Dictionary of all scored raw signals generated during the cycle, typically categorized by signal type (e.g., 'Directional', 'Volatility').")
    key_levels_data_v2_5: KeyLevelsDataV2_5 = Field(..., description="All identified key support, resistance, pin, and trigger levels with their conviction scores.")
    
    atif_recommendations_v2_5: Optional[List[ATIFStrategyDirectivePayloadV2_5]] = Field(default_factory=list, description="List of new strategic directives generated by ATIF in this cycle, *before* TPO parameterization. Useful for showing 'pending' ideas.")
    active_recommendations_v2_5: List[ActiveRecommendationPayloadV2_5] = Field(default_factory=list, description="The current list of all active and recently managed/closed trade recommendations for the symbol, fully parameterized by TPO.")
    
    bundle_timestamp: datetime = Field(..., description="Timestamp of when this final analysis bundle was created (marks the end of the analysis cycle).")
    target_symbol: str = Field(..., description="The ticker symbol this bundle pertains to.")
    system_status_messages: List[str] = Field(default_factory=list, description="Any system-level status messages, warnings, or errors generated during this analysis cycle.")

    class Config:
        extra = 'forbid' # This is a final, internal output structure
        arbitrary_types_allowed = True # Necessary because ProcessedDataBundleV2_5 contains ProcessedUnderlyingAggregatesV2_5, which holds a PandasDataFrame for ivsdh_surface_data.


class UnifiedIntelligenceAnalysis(BaseModel):
    """Unified intelligence analysis combining all AI systems."""
    symbol: str = Field(description="Trading symbol")
    timestamp: datetime = Field(default_factory=datetime.now, description="Analysis timestamp")
    confidence_score: float = Field(ge=0.0, le=1.0, description="Overall confidence score")
    market_regime_analysis: str = Field(description="Market regime analysis")
    options_flow_analysis: str = Field(description="Options flow analysis")
    sentiment_analysis: str = Field(description="Sentiment analysis")
    strategic_recommendations: List[str] = Field(default_factory=list, description="Strategic recommendations")
    risk_assessment: str = Field(description="Risk assessment")
    learning_insights: List[str] = Field(default_factory=list, description="Learning insights")
    performance_metrics: Dict[str, Any] = Field(default_factory=dict, description="Performance metrics")
    model_config = ConfigDict(extra="forbid")
